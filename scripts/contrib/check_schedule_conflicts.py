#!/usr/bin/env python3
"""Check for potential scheduling conflicts between monthly and daily reports.

This script analyzes existing YAML schedule configurations and their
corresponding JSON report configurations to identify monthly reports
that might be scheduled too close to daily reports sharing the same
Service Offering ID (SOID).

It iterates through production schedule YAMLs, finds monthly reports,
retrieves their SOIDs from the associated JSON configs, finds daily
reports linked to those SOIDs, and prints a comparison of their
scheduled times.
"""

from collections import defaultdict
import json
import os
from pathlib import Path
import sys
from typing import Any

import yaml

# --- Constants ---
# Determine project root assuming this script is in project_root/scripts/
PROJECT_ROOT = Path(__file__).resolve().parents[2]
JSON_CONFIG_DIR = PROJECT_ROOT / "payit_remittance_report" / "configs"

# Define environment schedule directories
ENV_SCHEDULE_DIRS: dict[str, Path] = {
    "Prod": PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "prod",
    "Staging": PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "staging",
    "CA Prod": PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "ca-prod",
    "CA Staging": PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "ca-staging",
}

# Current max job run time in minutes for checking daily vs monthly runs
MAX_JOB_RUN_TIME: int = 10

# List of monthly reports to exclude from schedule conflict checking
EXCLUDE_REPORTS: list[str] = [
    "beaufort_tax_monthly_v2_recap",
    "ncdmv_return_monthly_recap",
    "orange_county_ptax_monthly_financial_remittance",
]


# --- Helper Functions ---
def parse_yaml_file(filepath: Path) -> dict[str, Any] | None:
    """Safely parse a YAML file."""
    try:
        with open(filepath, "r") as f:
            return yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"Warning: Could not parse YAML file {filepath.name}: {e}")
    except FileNotFoundError:
        print(f"Warning: YAML file not found: {filepath}")
    except Exception as e:
        print(f"Warning: Error reading YAML file {filepath.name}: {e}")
    return None


def parse_json_file(filepath: Path) -> dict[str, Any] | None:
    """Safely parse a JSON file."""
    try:
        with open(filepath, "r") as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        print(f"Warning: Could not parse JSON file {filepath.name}: {e}")
    except FileNotFoundError:
        print(f"Warning: JSON file not found: {filepath}")
    except Exception as e:
        print(f"Warning: Error reading JSON file {filepath.name}: {e}")
    return None


def get_cron_time_str(cron_expression: str | None) -> str:
    """Extract HH:MM time string from a cron expression."""
    if not cron_expression:
        return "??:??"
    parts = cron_expression.split()
    if len(parts) >= 2:
        try:
            minute = int(parts[0])
            hour = int(parts[1])
            return f"{hour:02d}:{minute:02d}"
        except ValueError:
            return "Invalid Cron"
    return "Invalid Cron"


def get_cron_minutes(cron_expression: str | None) -> int | None:
    """Extract time as minutes from midnight from a cron expression."""
    if not cron_expression:
        return None
    parts = cron_expression.split()
    # Assert exactly 5 parts for standard cron format
    assert len(parts) == 5, f"Invalid cron expression format (expected 5 parts): {cron_expression}"
    try:
        minute = int(parts[0])
        hour = int(parts[1])
        # Basic validation for hour/minute ranges
        if 0 <= minute <= 59 and 0 <= hour <= 23:
            return hour * 60 + minute
        else:
            print(f"Warning: Invalid time components in cron: {cron_expression}")
            return None
    except ValueError:
        print(f"Warning: Non-integer time components in cron: {cron_expression}")
        return None


def get_cron_day_of_month(cron_expression: str | None) -> int | None:
    """Extract the day-of-month from a cron expression."""
    if not cron_expression:
        return None
    parts = cron_expression.split()
    # The day-of-month is the 3rd field (index 2)
    if len(parts) >= 3:
        dom_str = parts[2]
        # We only care about specific days for monthly reports
        if dom_str == "*":
            return None
        try:
            dom = int(dom_str)
            # Basic validation for day-of-month range
            if 1 <= dom <= 31:
                return dom
            else:
                print(f"Warning: Invalid day-of-month component in cron: {cron_expression}")
                return None
        except ValueError:
            print(f"Warning: Non-integer day-of-month component in cron: {cron_expression}")
            return None
    print(f"Warning: Could not parse day-of-month from cron: {cron_expression}")
    return None


# --- Table Formatting ---
COL_WIDTHS = {
    "monthly_report": 70,
    "monthly_time": 20,
    "daily_report": 70,
    "daily_time": 20,
}
TABLE_WIDTH = sum(COL_WIDTHS.values()) + (len(COL_WIDTHS) - 1) * 3


def print_table_header(title: str):
    """Print the header row for a conflict table with borders."""
    print("\n" + "=" * TABLE_WIDTH)
    print(f"===== {title} =====".center(TABLE_WIDTH))
    print("-" * TABLE_WIDTH)
    header = (
        f"{{:<{COL_WIDTHS['monthly_report']}}}   "
        f"{{:<{COL_WIDTHS['monthly_time']}}}   "
        f"{{:<{COL_WIDTHS['daily_report']}}}   "
        f"{{:<{COL_WIDTHS['daily_time']}}}"
    ).format(
        "Monthly Report",
        "Monthly Time",
        "Daily Report",
        "Daily Time",
    )
    print(header)
    print("-" * TABLE_WIDTH)


def print_table_row(monthly_report: str, monthly_time: str, daily_report: str, daily_time: str):
    """Print a data row for the conflict table."""
    row = (
        f"{{:<{COL_WIDTHS['monthly_report']}}}   "
        f"{{:<{COL_WIDTHS['monthly_time']}}}   "
        f"{{:<{COL_WIDTHS['daily_report']}}}   "
        f"{{:<{COL_WIDTHS['daily_time']}}}"
    ).format(monthly_report, monthly_time, daily_report, daily_time)
    print(row)


def load_schedules_from_dir(dir_path: Path) -> dict[str, dict[str, Any]]:
    """Load schedule data from YAML files in a specific directory."""
    schedules: dict[str, dict[str, Any]] = {}
    if not dir_path.is_dir():
        print(f"Warning: Schedule directory not found: {dir_path}")
        return schedules

    for filename in os.listdir(dir_path):
        if filename.endswith((".yaml", ".yml")):
            filepath = dir_path / filename
            schedule_data = parse_yaml_file(filepath)
            if schedule_data:
                report_name = schedule_data.get("arguments", {}).get("report-name")
                frequency = schedule_data.get("frequency")
                cron_expression = schedule_data.get("scheduleExpression")
                schedule_name = schedule_data.get("name")

                if report_name and frequency and cron_expression:
                    # Ensure report_name is unique, maybe warn/error if not? For now, last one wins.
                    schedules[report_name] = {
                        "frequency": frequency,
                        "cron": cron_expression,
                        "schedule_name": schedule_name or "Unknown Schedule Name",
                        "yaml_filename": filename,
                        "env_path": dir_path,  # Store origin path if needed later
                    }
                elif report_name:
                    # Only warn if freq/cron missing, not if report_name is missing
                    print(
                        f"Warning: Missing 'frequency' or 'scheduleExpression' in YAML "
                        f"{dir_path.name}/{filename} for report {report_name}"
                    )
    return schedules


def find_conflicts_for_environment(
    env_schedules: dict[str, dict[str, Any]],
    report_configs: dict[str, dict[str, Any]],
    soid_to_reports: dict[str, list[str]],
) -> tuple[list[dict[str, str]], list[dict[str, str]]]:
    """Find hard conflicts and proximity issues for a specific environment's schedules."""
    conflict_table_data: list[dict[str, str]] = []
    proximity_table_data: list[dict[str, str]] = []

    sorted_schedule_items = sorted(env_schedules.items())

    for report_name, schedule_info in sorted_schedule_items:
        if schedule_info["frequency"] == "monthly":
            # Skip reports in the exclusion list
            if report_name in EXCLUDE_REPORTS:
                continue

            monthly_cron = schedule_info["cron"]
            monthly_time_minutes = get_cron_minutes(monthly_cron)
            monthly_time_str = get_cron_time_str(monthly_cron)
            monthly_day_of_month = get_cron_day_of_month(monthly_cron)

            if monthly_time_minutes is None or monthly_day_of_month is None:
                continue

            monthly_config = report_configs.get(report_name)
            if not monthly_config:
                continue

            monthly_soids = monthly_config.get("soids", [])
            if not monthly_soids:
                continue

            # Store daily reports with potential hard conflicts
            related_daily_reports_with_hard_conflict: list[tuple[str, str, str, str, str]] = []

            processed_daily_reports: set[str] = set()
            for soid in monthly_soids:
                # Find related reports *across all schedules* (defined by JSON)
                related_reports = soid_to_reports.get(soid, [])
                for related_report_name in related_reports:
                    if related_report_name in processed_daily_reports:
                        continue

                    # Check schedule within the *current environment*
                    related_schedule = env_schedules.get(related_report_name)
                    if (
                        related_schedule
                        and related_schedule["frequency"] == "daily"
                        and related_report_name != report_name
                    ):
                        # Skip reports that have 'import' in the name but don't have 'remittance'
                        if "import" in related_report_name.lower() and "remittance" not in related_report_name.lower():
                            continue
                        processed_daily_reports.add(related_report_name)
                        daily_cron = related_schedule["cron"]
                        daily_time_minutes = get_cron_minutes(daily_cron)
                        daily_time_str = get_cron_time_str(daily_cron)

                        # --- Check 1: Hard Conflict (Day 1 Monthly <= Daily) ---
                        if (
                            monthly_day_of_month == 1
                            and daily_time_minutes is not None
                            and monthly_time_minutes <= daily_time_minutes
                        ):
                            related_daily_reports_with_hard_conflict.append(
                                (
                                    related_report_name,
                                    related_schedule["schedule_name"],
                                    related_schedule["yaml_filename"],
                                    daily_time_str,
                                    daily_cron,
                                )
                            )

                        # --- Check 2: Proximity Issue (Monthly < MAX_JOB_RUN_TIME min after Daily) ---
                        if daily_time_minutes is not None and monthly_time_minutes is not None:
                            time_diff_minutes = monthly_time_minutes - daily_time_minutes
                            if 0 < time_diff_minutes < MAX_JOB_RUN_TIME:
                                proximity_table_data.append(
                                    {
                                        "monthly_report": report_name,
                                        "monthly_time": monthly_time_str,
                                        "daily_report": related_report_name,
                                        "daily_time": daily_time_str,
                                    }
                                )

            # Store HARD conflict data for the first table
            if (
                related_daily_reports_with_hard_conflict  # No need to check day 1 again
            ):
                for (
                    daily_report_name,
                    _daily_schedule_name,
                    _daily_yaml_filename,
                    daily_time_str,
                    _daily_cron,
                ) in related_daily_reports_with_hard_conflict:
                    conflict_table_data.append(
                        {
                            "monthly_report": report_name,
                            "monthly_time": monthly_time_str,
                            "daily_report": daily_report_name,
                            "daily_time": daily_time_str,
                        }
                    )

    return conflict_table_data, proximity_table_data


def print_environment_report(
    env_name: str,
    conflict_data: list[dict[str, str]],
    proximity_data: list[dict[str, str]],
):
    """Print the two tables for a specific environment."""
    print(f"\n\n{'#' * 20} Environment: {env_name} {'#' * 20}")

    # --- Print Hard Conflicts Table ---
    if not conflict_data:
        print(
            f"\n[{env_name}] No potential schedule conflicts found "
            f"(where monthly on day 1 runs <= daily time for same SOID)."
        )
    else:
        table_title_hard = f"{env_name} - Potential Schedule Conflicts (Monthly Day 1 <= Daily Time)"
        print(f"\n[{env_name}] Found {len(conflict_data)} potential hard conflicts:")
        print_table_header(table_title_hard)
        conflict_data.sort(key=lambda x: (x["monthly_report"], x["daily_report"]))
        for row_data in conflict_data:
            print_table_row(
                row_data["monthly_report"],
                row_data["monthly_time"],
                row_data["daily_report"],
                row_data["daily_time"],
            )
        print("=" * TABLE_WIDTH)

    # --- Print Proximity Issues Table ---
    if not proximity_data:
        print(
            f"\n[{env_name}] No potential proximity issues found "
            f"(<{MAX_JOB_RUN_TIME}min gap between daily and monthly)."
        )
    else:
        table_title_prox = f"{env_name} - Potential Proximity Issues (Monthly runs < {MAX_JOB_RUN_TIME}min after Daily)"
        print(f"\n[{env_name}] Found {len(proximity_data)} potential proximity issues:")
        print_table_header(table_title_prox)
        proximity_data.sort(key=lambda x: (x["monthly_report"], x["daily_report"]))
        for row_data in proximity_data:
            print_table_row(
                row_data["monthly_report"],
                row_data["monthly_time"],
                row_data["daily_report"],
                row_data["daily_time"],
            )
        print("=" * TABLE_WIDTH)


def main() -> None:
    """Main function to find and compare monthly/daily report schedules across environments."""
    print("Starting schedule conflict check across all environments...")
    found_any_issue: bool = False
    all_environment_results: dict[str, tuple[list[dict[str, str]], list[dict[str, str]]]] = {}

    # 1. Load JSON Configs (assumed global)
    print(f"\nScanning JSON configs in: {JSON_CONFIG_DIR}")
    report_configs: dict[str, dict[str, Any]] = {}
    soid_to_reports: dict[str, list[str]] = defaultdict(list)
    # Define base names for which SOIDs are expected
    expected_soid_base_names = {"financial_remittance", "ebilling_remittance", "remittance_311"}
    if not JSON_CONFIG_DIR.is_dir():
        print(f"Error: JSON config directory not found: {JSON_CONFIG_DIR}")
        return
    else:
        for filename in os.listdir(JSON_CONFIG_DIR):
            if filename.endswith(".json"):
                filepath = JSON_CONFIG_DIR / filename
                config_data = parse_json_file(filepath)
                if config_data:
                    report_name = config_data.get("report_name")
                    soids = config_data.get("service_offering_ids")
                    base_name = config_data.get("base_name")  # Get base_name

                    if report_name and isinstance(soids, list):
                        report_configs[report_name] = {"soids": soids}
                        for soid in soids:
                            soid_to_reports[soid].append(report_name)
                    # Only warn if report_name exists, SOIDs are missing/invalid, AND base_name requires SOIDs
                    elif report_name and base_name in expected_soid_base_names:
                        print(
                            f"Warning: Missing or invalid 'service_offering_ids' in {filename} (base_name: {base_name})"
                        )

    # 2. Load Schedules and Find Conflicts per Environment
    print("\nScanning YAML schedules and analyzing for conflicts...")
    for env_name, schedule_dir in ENV_SCHEDULE_DIRS.items():
        print(f"Processing environment: {env_name} (Path: {schedule_dir})")
        env_schedules = load_schedules_from_dir(schedule_dir)
        if not env_schedules:
            print(f"  No schedules loaded for {env_name}. Skipping analysis.")
            all_environment_results[env_name] = ([], [])  # Store empty results
            continue

        conflicts, proximity = find_conflicts_for_environment(env_schedules, report_configs, soid_to_reports)

        all_environment_results[env_name] = (conflicts, proximity)

        if conflicts or proximity:
            found_any_issue = True
            print(f"  Found potential issues in {env_name}.")
        else:
            print(f"  No issues found in {env_name}.")

    # 3. Print Reports per Environment
    # Ensure consistent order for printing
    sorted_env_names = sorted(ENV_SCHEDULE_DIRS.keys(), key=lambda x: ("CA" not in x, x))
    for env_name in sorted_env_names:
        conflicts, proximity = all_environment_results[env_name]
        print_environment_report(env_name, conflicts, proximity)

    print("\nSchedule conflict check finished.")

    # 4. Exit with error code if any issues were found
    if found_any_issue:
        print("\nExiting due to detected schedule conflicts or proximity issues.")
        sys.exit(1)


if __name__ == "__main__":
    main()
