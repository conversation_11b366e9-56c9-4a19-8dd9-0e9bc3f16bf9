"""Environment variable utilities for report configuration generation."""

import os
from pathlib import Path

from dotenv import load_dotenv

from scripts.contrib.create_report_configs.models.constants import PROJECT_ROOT


def read_env_variable(var_name: str) -> str | None:
    """Read an environment variable from the system environment or .env file.

    This function first checks system environment variables, then loads from
    a .env file if the variable is not found in the system environment.

    Args:
        var_name (str): The name of the environment variable to read.

    Returns:
        str | None: The value of the environment variable, or None if not found.
    """
    # First try to get from system environment
    value = os.getenv(var_name)
    if value is not None:
        return value

    # Load .env file and try again
    env_file_path = PROJECT_ROOT / ".env"
    if env_file_path.exists():
        load_dotenv(env_file_path)
        return os.getenv(var_name)

    return None


def load_env_file(env_file_path: Path | None = None) -> bool:
    """Load environment variables from a .env file.

    Args:
        env_file_path (Path | None): Path to the .env file. If None, uses PROJECT_ROOT/.env.

    Returns:
        bool: True if the .env file was loaded successfully, False otherwise.
    """
    if env_file_path is None:
        env_file_path = PROJECT_ROOT / ".env"

    if env_file_path.exists():
        load_dotenv(env_file_path)
        return True

    return False
