"""Utility module for finding the most recent transaction date for a given SOID."""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import yaml
from sqlalchemy import CursorResult

from fine_reports.ports.logging import FineReportsLogger, get_logger

logger: FineReportsLogger = get_logger(__name__)


def _setup_environment_for_snowflake() -> None:
    """Set up the minimal environment configuration required for Snowflake access.

    This function ensures that the necessary environment variables are set
    for the SnowpawDatastore to work properly when called from the GUI context.
    """
    # Set ENV_FOR_DYNACONF to staging if not already set
    if not os.environ.get("ENV_FOR_DYNACONF"):
        os.environ["ENV_FOR_DYNACONF"] = "staging"
        logger.info("Set ENV_FOR_DYNACONF=staging for Snowflake access")

    # Set minimal required environment variables with FR_ prefix
    required_env_vars = {
        "FR_BASE_NAME": "financial_remittance",
        "FR_REPORT_NAME": "auto_find_transaction_date_query",
        "FR_REPORT_DATE": "2025-01-01",  # Dummy date, not used for our query
        "FR_LOCAL_RUN": "true",
        "FR_ENABLE_DELIVERY": "false",
        "FR_SAVE_REMITTANCE_DATA": "false",
        "FR_USE_SNOWFLAKE": "true",  # Enable Snowflake usage
    }

    for env_var, default_value in required_env_vars.items():
        if not os.environ.get(env_var):
            os.environ[env_var] = default_value
            logger.debug(f"Set {env_var}={default_value}")


def find_latest_transaction_date(soid: str) -> Optional[str]:
    """Find the most recent transaction date for a given service offering ID.
    
    Args:
        soid (str): The service offering ID to query for.
        
    Returns:
        Optional[str]: The most recent transaction date in YYYY-MM-DD format, 
                      or None if no transactions found or error occurred.
    """
    if not soid or not soid.strip():
        logger.warning("Empty SOID provided to find_latest_transaction_date")
        return None
        
    try:
        logger.info(f"Querying Snowflake for latest transaction date for SOID: {soid}")

        # Set up environment for Snowflake access
        _setup_environment_for_snowflake()

        # Import SnowpawDatastore after environment setup to avoid import-time validation errors
        from fine_reports.datastore.snowpaw import SnowpawDatastore

        # Create Snowflake datastore instance
        snowpaw_datastore = SnowpawDatastore()
        
        # Execute the query
        cursor_result: CursorResult = snowpaw_datastore.named_query(
            "find_latest_transaction_date",
            soid=soid
        )
        
        # Get the first result
        first_result = cursor_result.first()
        
        if first_result is None:
            logger.info(f"No transactions found for SOID: {soid}")
            return None
            
        # Extract the transaction date
        result_dict = dict(first_result._mapping)
        transaction_date = result_dict.get("transaction_date")
        
        if transaction_date is None:
            logger.warning(f"Transaction date is None for SOID: {soid}")
            return None
            
        # Convert to string format YYYY-MM-DD
        if isinstance(transaction_date, datetime):
            date_str = transaction_date.strftime("%Y-%m-%d")
        else:
            # Handle case where it's already a string - extract just the date part
            date_str = str(transaction_date)
            if ' ' in date_str:
                date_str = date_str.split(' ')[0]  # Take only the date part before the space
            
        logger.info(f"Found latest transaction date for SOID {soid}: {date_str}")
        return date_str
        
    except Exception as e:
        logger.error(f"Error finding latest transaction date for SOID {soid}: {str(e)}")
        return None


def get_report_frequency(report_name: str) -> str:
    """Get the frequency (daily/monthly) for a given report name by checking YAML configs.
    
    Args:
        report_name (str): The report name to look up.
        
    Returns:
        str: The frequency ("daily", "monthly", or "daily" as default).
    """
    if not report_name or not report_name.strip():
        logger.warning("Empty report name provided to get_report_frequency")
        return "daily"
        
    try:
        # Path to scheduled job configurations
        repo_root = Path(__file__).resolve().parent.parent.parent.parent.parent
        staging_dir = repo_root / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "staging"
        
        if not staging_dir.exists():
            logger.warning(f"Staging directory not found: {staging_dir}")
            return "daily"
            
        # Search through all YAML files in the staging directory
        for yaml_file in staging_dir.glob("*.yaml"):
            try:
                with yaml_file.open("r") as f:
                    config = yaml.safe_load(f)
                    
                if not config or not isinstance(config, dict):
                    continue
                    
                # Check if this config matches our report name
                arguments = config.get("arguments", {})
                config_report_name = arguments.get("report-name", "")
                
                if config_report_name == report_name:
                    frequency = config.get("frequency", "daily")
                    logger.info(f"Found frequency '{frequency}' for report: {report_name}")
                    return frequency
                    
            except Exception as e:
                logger.warning(f"Error reading YAML file {yaml_file}: {str(e)}")
                continue
                
        logger.info(f"No configuration found for report '{report_name}', defaulting to daily")
        return "daily"
        
    except Exception as e:
        logger.error(f"Error determining report frequency for {report_name}: {str(e)}")
        return "daily"


def calculate_next_month_first_date(date_str: str) -> str:
    """Calculate the first day of the month immediately following a given date.

    Args:
        date_str (str): Date string in YYYY-MM-DD format.

    Returns:
        str: First day of the next month in YYYY-MM-DD format.
    """
    try:
        # Parse the input date
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")

        # Calculate next month
        if date_obj.month == 12:
            # Handle year rollover
            next_month_first = date_obj.replace(year=date_obj.year + 1, month=1, day=1)
        else:
            next_month_first = date_obj.replace(month=date_obj.month + 1, day=1)

        return next_month_first.strftime("%Y-%m-%d")

    except Exception as e:
        logger.error(f"Error calculating next month first date for {date_str}: {str(e)}")
        return date_str  # Return original date if calculation fails


def get_auto_find_date_for_report(report_name: str, soid: str) -> Optional[str]:
    """Get the appropriate auto-find date for a report based on its frequency.

    For daily reports: Returns the exact latest transaction date.
    For monthly reports: Returns the first day of the month immediately following the latest transaction date.

    Args:
        report_name (str): The report name to determine frequency.
        soid (str): The service offering ID to query for.

    Returns:
        Optional[str]: The calculated date in YYYY-MM-DD format, or None if error.
    """
    # Get the latest transaction date
    latest_date = find_latest_transaction_date(soid)
    if latest_date is None:
        return None
        
    # Get the report frequency
    frequency = get_report_frequency(report_name)
    
    # Calculate the appropriate date based on frequency
    if frequency == "monthly":
        return calculate_next_month_first_date(latest_date)
    else:
        return latest_date


def find_latest_transaction_dates_batch(soids: List[str]) -> Dict[str, Optional[str]]:
    """Find the most recent transaction dates for multiple service offering IDs in a single query.

    Args:
        soids (List[str]): List of service offering IDs to query for.

    Returns:
        Dict[str, Optional[str]]: Dictionary mapping SOID to latest transaction date in YYYY-MM-DD format.
                                 SOIDs with no transactions will have None values.
    """
    if not soids:
        logger.warning("Empty SOID list provided to find_latest_transaction_dates_batch")
        return {}

    # Remove empty/None SOIDs and deduplicate
    valid_soids = list(set(soid.strip() for soid in soids if soid and soid.strip()))
    if not valid_soids:
        logger.warning("No valid SOIDs provided to find_latest_transaction_dates_batch")
        return {}

    try:
        logger.info(f"Querying Snowflake for latest transaction dates for {len(valid_soids)} SOIDs")

        # Set up environment for Snowflake access
        _setup_environment_for_snowflake()

        # Import SnowpawDatastore after environment setup to avoid import-time validation errors
        from fine_reports.datastore.snowpaw import SnowpawDatastore

        # Create Snowflake datastore instance
        snowpaw_datastore = SnowpawDatastore()

        # Execute the batch query
        cursor_result: CursorResult = snowpaw_datastore.named_query(
            "find_latest_transaction_dates_batch",
            soids=valid_soids
        )

        # Process results into a dictionary
        results = {}
        for row in cursor_result:
            row_dict = dict(row._mapping)
            soid = row_dict.get("service_offering_id")
            transaction_date = row_dict.get("transaction_date")

            if soid and transaction_date:
                # Convert to string format YYYY-MM-DD
                if isinstance(transaction_date, datetime):
                    date_str = transaction_date.strftime("%Y-%m-%d")
                else:
                    # Handle case where it's already a string - extract just the date part
                    date_str = str(transaction_date)
                    if ' ' in date_str:
                        date_str = date_str.split(' ')[0]  # Take only the date part before the space

                results[soid] = date_str
                logger.info(f"Found latest transaction date for SOID {soid}: {date_str}")

        # Ensure all requested SOIDs are in the results (with None for those not found)
        for soid in valid_soids:
            if soid not in results:
                results[soid] = None
                logger.info(f"No transactions found for SOID: {soid}")

        return results

    except Exception as e:
        logger.error(f"Error querying Snowflake for batch transaction dates: {str(e)}")
        # Return dictionary with None values for all SOIDs on error
        return {soid: None for soid in valid_soids}


def get_auto_find_dates_for_reports_batch(report_soid_pairs: List[Tuple[str, str]]) -> Dict[str, Optional[str]]:
    """Get appropriate auto-find dates for multiple reports based on their frequencies.

    This function optimizes performance by batching SOID queries and processing them together.

    Args:
        report_soid_pairs (List[Tuple[str, str]]): List of (report_name, soid) tuples.

    Returns:
        Dict[str, Optional[str]]: Dictionary mapping report_name to calculated date in YYYY-MM-DD format.
    """
    if not report_soid_pairs:
        return {}

    # Extract unique SOIDs for batch query
    soids = list(set(soid for _, soid in report_soid_pairs if soid and soid.strip()))

    if not soids:
        logger.warning("No valid SOIDs found in report_soid_pairs")
        return {report_name: None for report_name, _ in report_soid_pairs}

    # Get latest transaction dates for all SOIDs in batch
    soid_to_date = find_latest_transaction_dates_batch(soids)

    # Process each report individually to apply frequency-based logic
    results = {}
    for report_name, soid in report_soid_pairs:
        if not soid or not soid.strip():
            results[report_name] = None
            continue

        latest_date = soid_to_date.get(soid.strip())
        if latest_date is None:
            results[report_name] = None
            continue

        # Get the report frequency and calculate appropriate date
        frequency = get_report_frequency(report_name)

        if frequency == "monthly":
            results[report_name] = calculate_next_month_first_date(latest_date)
        else:
            results[report_name] = latest_date

    return results
