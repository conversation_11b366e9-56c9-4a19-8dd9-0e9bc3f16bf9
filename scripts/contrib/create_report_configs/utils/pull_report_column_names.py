#!/usr/bin/env python3
"""Script to generate REPORT_ATTRIBS for monthly recap scripts.

This script extracts column names from remittance reports and generates
the REPORT_ATTRIBS structure needed for monthly recap custom Python scripts.

Usage:
    ENV_FOR_DYNACONF=staging poetry run python scripts/contrib/create_report_configs/utils/pull_report_column_names.py \
        --report_name=buffalo_ny_zoning_forms_financial_remittance \
        --report_date=2025-04-24

The script discovers report column names using RemittanceReport engine discovery.
"""

import argparse
import importlib
import json
from pathlib import Path
import sys
from typing import Any

from fine_reports.ports.logging import FineReportsLogger, get_logger

logger: FineReportsLogger = get_logger(__name__)


# Determine project root path
PROJECT_ROOT = Path(__file__).resolve().parents[4]


def _discover_columns_via_engine(report_name: str, report_date: str) -> dict[str, list[str]]:
    """Run RemittanceReport logic to dynamically gather column names.

    Args:
        report_name (str): Daily report config name.
        report_date (str): Date string YYYY-MM-DD to run against.

    Returns:
        dict[str, list[str]]: mapping of report_type -> column names.
    """
    try:
        logger.info(f"Attempting to import RemittanceReport for report_name={report_name}")
        rr_module = importlib.import_module("payit_remittance_report.remittance_report")
        RemittanceReport = getattr(rr_module, "RemittanceReport")
        BASE_FIELDS = getattr(rr_module, "BASE_FIELDS")
        BASE_FIELDS_RETURN = getattr(rr_module, "BASE_FIELDS_RETURN")

        # Check for daily JSON config
        project_root = Path(__file__).resolve().parents[4]
        json_config_dir = project_root / "payit_remittance_report" / "configs"
        daily_json_path = json_config_dir / f"{report_name}.json"
        if daily_json_path.exists():
            logger.info(f"Found daily JSON config: {daily_json_path}")
        else:
            logger.info(f"Daily JSON config NOT found: {daily_json_path}")

        rep = RemittanceReport(report_name=report_name)
        logger.info(f"Initialized RemittanceReport for {report_name}")

        # set dates
        rep._report_date = report_date
        rep._begin_date = report_date
        rep._end_date = report_date

        logger.info(f"Calling _get_report_data for date={report_date}")
        rep._get_report_data(date=report_date)

        if hasattr(rep, "_report_data"):
            row_count = len(rep._report_data) if hasattr(rep._report_data, "__len__") else "unknown"
            logger.info(f"Fetched report data: {row_count} rows for date {report_date}")
        else:
            logger.info("RemittanceReport has no _report_data attribute after _get_report_data call")

        base_names = [c["name"] for c in BASE_FIELDS]
        additional_names = [f["name"] for f in rep._report_configs.get("additional_fields", [])]
        dynamic_names = list(rep._metadata_headers)

        logger.info(f"Base columns: {len(base_names)} names: {base_names}")
        logger.info(f"Additional columns: {len(additional_names)} names: {additional_names}")
        logger.info(f"Dynamic columns: {len(dynamic_names)} names: {dynamic_names}")

        if not base_names:
            logger.info("Base columns list is empty!")
        if not additional_names:
            logger.info("Additional columns list is empty!")
        if not dynamic_names:
            logger.info("Dynamic columns list is empty!")

        common = base_names + additional_names + dynamic_names

        cfg = rep._report_configs
        result: dict[str, list[str]] = {}

        if "SUCCESS" in cfg.get("success", []):
            result["success"] = common.copy()
        if "RETURN" in cfg.get("failure", []) or "RETURN" in cfg.get("cancellation", []):
            result["return"] = common + [c["name"] for c in BASE_FIELDS_RETURN]
        if "COMBINED" in cfg.get("success", []):
            result["combined"] = common + [c["name"] for c in BASE_FIELDS_RETURN]

        logger.info(f"Discovered report types: {list(result.keys())}")
        for k, v in result.items():
            logger.info(f"Report type '{k}' has {len(v)} columns: {v}")

        return result
    except Exception as exc:
        logger.info(f"Engine column discovery failed: {exc}")
        return {}


def generate_report_attribs(
    column_names: dict[str, list[str]],
    report_name: str,
    advanced_selections: dict[str, Any] | None,
    remittance_type: str | None,
) -> dict[str, Any]:
    """Generate the REPORT_ATTRIBS structure for monthly recap scripts.

    Args:
        column_names (dict[str, list[str]]): Dictionary of column names by report type.
        report_name (str): The name of the report.
        advanced_selections (dict[str, Any] | None): Advanced remittance selections.
        remittance_type (str | None): Remittance type (Combined or Return).

    Returns:
        dict[str, Any]: The REPORT_ATTRIBS structure for monthly recap scripts.
    """
    # Determine report types
    report_types = list(column_names.keys())

    # Check if we need to add report_columns_return for Combined remittance type
    needs_return_columns = False
    if remittance_type == "Combined" and advanced_selections:
        # Check if any advanced selection contains "RETURN"
        for selection_value in advanced_selections.values():
            if selection_value and "RETURN" in str(selection_value):
                needs_return_columns = True
                break

    # If we need return columns but don't have "return" in report_types, add it
    if needs_return_columns and "return" not in report_types:
        # For monthly Combined reports with RETURN advanced selections,
        # the return columns should be identical to combined columns
        if "combined" in column_names:
            column_names["return"] = column_names["combined"].copy()
            report_types.append("return")

    # --------------------------------------------------------------
    # Establish the base filename for downstream file-matching.
    # --------------------------------------------------------------

    base_filename = report_name.replace("_financial_remittance", "")  # default legacy behaviour

    # ------------------------------------------------------------------
    # Determine if the associated daily report includes a summary section
    # ------------------------------------------------------------------

    has_summaries: bool = True  # default to True; overridden if we can inspect JSON

    try:
        # Derive project root (scripts/contrib/create_report_configs/utils/ -> repo root)
        project_root = Path(__file__).resolve().parents[4]
        json_config_dir = project_root / "payit_remittance_report" / "configs"

        daily_json_path = json_config_dir / f"{report_name}.json"

        if daily_json_path.exists():
            with open(daily_json_path) as jf:
                daily_config = json.load(jf)

            # Presence of the "summary" key determines summaries support
            has_summaries = "summary" in daily_config

            # If the daily config defines an explicit output_file_name, prefer
            # that over the underscored report_name-derived default.
            explicit_output: str | None = daily_config.get("output_file_name")
            if explicit_output:
                base_filename = explicit_output
        else:
            # If the daily JSON isn't found we assume no summaries
            raise FileNotFoundError(f"Daily JSON config not found for {report_name}")
    except Exception as exc:
        # On any error, raise
        print(f"Warning: could not evaluate summaries presence for {report_name}: {exc}.")
        raise

    # ------------------------------------------------------------------
    # Create the REPORT_ATTRIBS structure
    # ------------------------------------------------------------------
    report_attribs = {
        "report": {
            "types": report_types,
            "filter_mask": f"payit-{base_filename}-TYPE",
            "out_filename": f"payit-{base_filename}-TYPE-monthly_recap-DATE.csv",
            "sort_columns": ["Transaction Date", "Transaction ID"],
            "transaction_id_column": "Transaction ID",
            "groupby_column": "Payment Type",
            "has_summaries": has_summaries,
            "has_dynamic_summary": True if has_summaries else False,
            "summary_amount_source_column": "Transaction ID",
            "summary_count_source_column": "Transaction ID",
            "amount_column": "Remittance Amount",
            "summary_titles": ["CC", "ACH"],
            "summary_stock_titles": ["Summary", "Total"],
        }
    }

    # Add column definitions for each report type
    for report_type in report_types:
        field_name = f"report_columns_{report_type}"
        report_attribs["report"][field_name] = column_names[report_type]

    return report_attribs


def format_python_dict(data: dict[str, Any], indent: int = 4) -> str:
    """Format a dictionary as a Python code string with proper indentation.

    Args:
        data (dict[str, Any]): The dictionary to format.
        indent (int): The indentation level.

    Returns:
        str: The formatted Python code string.
    """
    result = []

    def _format_value(value, current_indent):
        indent_str = " " * current_indent

        if isinstance(value, dict):
            dict_items = []
            dict_items.append("{")
            for k, v in value.items():
                formatted_value = _format_value(v, current_indent + indent)
                dict_items.append(f'{indent_str}    "{k}": {formatted_value},')
            dict_items.append(f"{indent_str}}}")
            return "\n".join(dict_items)

        elif isinstance(value, list):
            if not value:
                return "[]"

            if isinstance(value[0], str):
                # Format strings in list with proper quotes
                items = [f'"{item}"' for item in value]
                if len(", ".join(items)) > 80:  # Line too long
                    list_items = []
                    list_items.append("[")
                    for item in items:
                        list_items.append(f"{indent_str}    {item},")
                    list_items.append(f"{indent_str}]")
                    return "\n".join(list_items)
                else:
                    return f"[{', '.join(items)}]"
            else:
                return f"{value}"

        elif isinstance(value, str):
            return f'"{value}"'
        else:
            return f"{value}"

    result.append("REPORT_ATTRIBS = {")
    for key, value in data.items():
        formatted_value = _format_value(value, indent)
        result.append(f'{" " * indent}"{key}": {formatted_value},')
    result.append("}")

    return "\n".join(result)


def main() -> None:
    """Main function to generate REPORT_ATTRIBS for monthly recap scripts."""
    parser = argparse.ArgumentParser(description="Generate REPORT_ATTRIBS for monthly recap scripts")
    parser.add_argument("--report_name", required=True, help="Name of the report")
    parser.add_argument("--report_date", required=True, help="Report date (YYYY-MM-DD)")
    parser.add_argument("--advanced_selections", help="JSON string of advanced remittance selections")
    parser.add_argument("--remittance_type", help="Remittance type (Combined or Return)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    args = parser.parse_args()

    # Configure logging level based on verbose flag
    log_level = "INFO" if not args.verbose else "DEBUG"
    logger.remove()
    logger.add(sys.stderr, level=log_level)

    report_name = args.report_name
    report_date = args.report_date

    # Parse advanced selections if provided
    advanced_selections = None
    if args.advanced_selections:
        try:
            advanced_selections = json.loads(args.advanced_selections)
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse advanced_selections JSON: {e}")

    logger.info(f"Loading report configuration for: {report_name}")

    # Set environment variable for Dynaconf
    import os

    os.environ.setdefault("ENV_FOR_DYNACONF", "staging")

    # Use engine-based column discovery
    logger.info("Attempting column discovery via RemittanceReport engine")
    column_names = _discover_columns_via_engine(report_name, report_date)

    if column_names:
        logger.info("Successfully discovered columns via engine")

        # Deduplicate columns while preserving order for each report type
        for rpt_type, cols in column_names.items():
            seen: set[str] = set()
            deduped: list[str] = []
            for col in cols:
                if col not in seen:
                    deduped.append(col)
                    seen.add(col)
            column_names[rpt_type] = deduped

        # Generate REPORT_ATTRIBS
        report_attribs = generate_report_attribs(column_names, report_name, advanced_selections, args.remittance_type)

        # Format as Python code and print
        formatted_code = format_python_dict(report_attribs)
        print(f"\nREPORT_ATTRIBS for {report_name}:\n")
        print(formatted_code)
    else:
        logger.info("No columns discovered via engine. Initializing empty report_columns list")
        print(f"No column definitions found for report: {report_name}")
        sys.exit(1)


if __name__ == "__main__":
    main()
