"""File operations utilities for report configuration generation."""

import json
from pathlib import Path
from typing import Any, Callable, override

import yaml
from yaml import Dumper

from scripts.contrib.create_report_configs.models.constants import (
    JSON_CONFIG_DIR,
    PROD_SCHEDULED_JOB_DIR,
)


class YamlListIndenter(Dumper):
    """Custom YAML Dumper to control list indentation."""

    @override
    def increase_indent(self, flow: bool = False, indentless: bool = False) -> None:
        """Increase indentation level, overriding base method.

        Ensures list items are indented correctly.

        Args:
            flow (bool): Whether the context is a flow style.
            indentless (bool): Whether the context is indentless.
        """
        super(YamlListIndenter, self).increase_indent(flow, False)


def str_presenter(dumper: Dumper, data: str):
    """Custom string presenter for YAML."""
    if len(data.splitlines()) > 1:  # check for multiline string
        return dumper.represent_scalar("tag:yaml.org,2002:str", data, style="|")
    return dumper.represent_scalar("tag:yaml.org,2002:str", data)


yaml.add_representer(str, str_presenter)


def write_json_file(filepath: Path, data: dict[str, Any]) -> None:
    """Write JSON data to a file.

    Args:
        filepath (Path): Path to write the JSON file to.
        data (dict[str, Any]): Data to write as JSON.

    Raises:
        IOError: If file cannot be written.
    """
    try:
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            f.write("\n")
    except Exception as e:
        raise IOError(f"Failed to write JSON file {filepath}: {e}") from e


def write_yaml_file(filepath: Path, data: dict[str, Any]) -> None:
    """Write YAML data to a file.

    Args:
        filepath (Path): Path to write the YAML file to.
        data (dict[str, Any]): Data to write as YAML.

    Raises:
        IOError: If file cannot be written.
    """
    try:
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, "w", encoding="utf-8") as f:
            yaml.dump(data, f, Dumper=YamlListIndenter, default_flow_style=False, sort_keys=False)
    except Exception as e:
        raise IOError(f"Failed to write YAML file {filepath}: {e}") from e


def write_text_file(filepath: Path, content: str) -> None:
    """Write text content to a file.

    Args:
        filepath (Path): Path to write the text file to.
        content (str): Content to write.

    Raises:
        IOError: If file cannot be written.
    """
    try:
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
    except Exception as e:
        raise IOError(f"Failed to write text file {filepath}: {e}") from e


def read_text_file(filepath: Path) -> str:
    """Read text content from a file.

    Args:
        filepath (Path): Path to read the text file from.

    Returns:
        str: File content.

    Raises:
        IOError: If file cannot be read.
    """
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        raise IOError(f"Failed to read text file {filepath}: {e}") from e


def check_existing_files(*filepaths: Path | None) -> list[Path]:
    """Check which of the provided file paths already exist.

    Args:
        *filepaths (Path | None): File paths to check for existence.

    Returns:
        list[Path]: List of paths that exist.
    """
    existing_files = []
    for filepath in filepaths:
        if filepath and filepath.exists():
            existing_files.append(filepath)
    return existing_files


def find_conflicting_daily_schedules(soid: str) -> list[str]:
    """Find conflicting daily schedule times for a given SOID.

    This function searches through existing YAML files to find
    daily schedules that might conflict with a new monthly schedule.

    Args:
        soid (str): The service offering ID to search for.

    Returns:
        list[str]: List of cron expressions for conflicting schedules.
    """
    conflicting_times = []

    try:
        # Search in production scheduled job directory
        if PROD_SCHEDULED_JOB_DIR.exists():
            for yaml_file in PROD_SCHEDULED_JOB_DIR.glob("*_daily_client.yaml"):
                try:
                    with open(yaml_file, "r", encoding="utf-8") as f:
                        yaml_content = yaml.safe_load(f)

                    # Check if this YAML file contains the SOID
                    if isinstance(yaml_content, dict):
                        arguments = yaml_content.get("arguments", {})
                        report_name = arguments.get("report-name", "")

                        # Check if the report name contains the SOID
                        if soid in report_name and yaml_content.get("frequency") == "daily":
                            schedule_expr = yaml_content.get("scheduleExpression", "")
                            if schedule_expr:
                                conflicting_times.append(schedule_expr)

                except Exception as e:
                    # Log warning but continue processing other files
                    print(f"Warning: Could not process YAML file {yaml_file}: {e}")

    except Exception as e:
        print(f"Warning: Could not search for conflicting schedules: {e}")

    return conflicting_times


def check_for_generated_files(project_root: Path, log_callback: Callable[[str, str], None] | None = None) -> bool:
    """Check if any files were recently generated in common output directories.

    Args:
        project_root (Path): The project root directory.
        log_callback (callable): Optional logging callback function.

    Returns:
        bool: True if recent files were found, False otherwise.
    """
    import time

    # Current time minus 60 seconds (files generated in last minute)
    cutoff_time = time.time() - 60

    # Common output directories to check
    output_dirs = [project_root, project_root / "output", project_root / "reports", Path.home() / "Downloads"]

    for output_dir in output_dirs:
        if not output_dir.exists():
            continue

        try:
            # Look for CSV, Excel, and PDF files modified in the last minute
            for pattern in ["*.csv", "*.xlsx", "*.xls", "*.pdf"]:
                for file_path in output_dir.glob(pattern):
                    if file_path.stat().st_mtime > cutoff_time:
                        if log_callback:
                            log_callback(f"Generated file found: {file_path}", "blue")
                        return True
        except Exception:
            # Ignore permission errors and continue checking other directories
            continue

    return False


def get_columns_from_report_config(report_name: str, soid: str) -> dict[str, list[str]]:
    """Get column definitions directly from the report configuration.

    Args:
        report_name (str): The name of the report.
        soid (str): Service offering ID.

    Returns:
        dict[str, list[str]]: Dictionary of columns by report type.
    """
    try:
        # Import here to avoid global dependency
        from payit_remittance_report.remittance_report import BASE_FIELDS, BASE_FIELDS_RETURN

        # Get the report configuration
        json_config_path = JSON_CONFIG_DIR / f"{report_name}.json"
        if not json_config_path.exists():
            print(f"Warning: Report configuration file not found: {json_config_path}")
            return {}

        with open(json_config_path) as f:
            report_config = json.load(f)

        result = {}

        # Process based on report types
        if "SUCCESS" in report_config.get("success", []):
            # Get base fields
            success_fields = [col["name"] for col in BASE_FIELDS]

            # Add additional fields from config
            if report_config.get("additional_fields"):
                for field in report_config["additional_fields"]:
                    success_fields.append(field["name"])

            # Do NOT inject any extra generic metadata fields here. Column discovery for forms/SDK reports
            # is handled dynamically by `pull_report_column_names.py` when generating monthly integrations.
            # Only fields explicitly defined in the configuration are included at this stage.

            result["success"] = success_fields

        if "RETURN" in report_config.get("failure", []) or "RETURN" in report_config.get("cancellation", []):
            # Start with success fields if they exist, otherwise base fields
            return_fields = result.get("success", [col["name"] for col in BASE_FIELDS]).copy()

            # Add return-specific fields
            return_fields.extend([col["name"] for col in BASE_FIELDS_RETURN])

            result["return"] = return_fields

        if "COMBINED" in report_config.get("success", []):
            # Start with all fields
            combined_fields = [col["name"] for col in BASE_FIELDS]

            # Add additional fields from config
            if report_config.get("additional_fields"):
                for field in report_config["additional_fields"]:
                    combined_fields.append(field["name"])

            # Add return fields
            combined_fields.extend([col["name"] for col in BASE_FIELDS_RETURN])

            result["combined"] = combined_fields

        # If we couldn't determine report types, use reasonable defaults
        if not result:
            result["success"] = [col["name"] for col in BASE_FIELDS]
            return_fields = [col["name"] for col in BASE_FIELDS]
            return_fields.extend([col["name"] for col in BASE_FIELDS_RETURN])
            result["return"] = return_fields

        return result
    except Exception as e:
        print(f"Error getting column definitions: {e}")
        return {}
