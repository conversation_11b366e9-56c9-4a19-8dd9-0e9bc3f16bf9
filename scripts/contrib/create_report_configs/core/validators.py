"""Business logic validators for report configuration generation."""

from PySide6.QtCore import QTime
import yaml

from scripts.contrib.create_report_configs.models.constants import (
    DAILY_REPORT_SUFFIX,
    MONTHLY_REPORT_SUFFIX,
    PROD_SCHEDULED_JOB_DIR,
    STAGING_SCHEDULED_JOB_DIR,
)
from scripts.contrib.create_report_configs.models.report_config import FormData, RunReportData
from scripts.contrib.create_report_configs.utils.file_operations import find_conflicting_daily_schedules
from scripts.contrib.create_report_configs.utils.scheduling import (
    validate_dual_frequency_timing,
    validate_schedule_conflicts,
)


def _get_daily_report_schedule_time(daily_report_name: str) -> QTime | None:
    """Get the schedule time for an existing daily report from its YAML configuration.

    Args:
        daily_report_name (str): The name of the daily report.

    Returns:
        QTime | None: The schedule time of the daily report, or None if not found.
    """
    yaml_filename = f"{daily_report_name}_daily_client.yaml"

    # Check prod first, then staging as fallback
    for config_dir in [PROD_SCHEDULED_JOB_DIR, STAGING_SCHEDULED_JOB_DIR]:
        yaml_filepath = config_dir / yaml_filename

        if not yaml_filepath.exists():
            continue

        try:
            with open(yaml_filepath, "r", encoding="utf-8") as f:
                yaml_content = yaml.safe_load(f)

            if not isinstance(yaml_content, dict):
                continue

            # Extract the cron schedule expression
            cron_expression = yaml_content.get("scheduleExpression")
            if not cron_expression:
                continue

            # Parse the cron expression (format: "minute hour * * *")
            parts = cron_expression.split()
            if len(parts) < 2:
                continue

            try:
                minute = int(parts[0])
                hour = int(parts[1])

                # Validate ranges
                if 0 <= minute <= 59 and 0 <= hour <= 23:
                    return QTime(hour, minute)
            except ValueError:
                continue

        except Exception as e:
            print(f"Warning: Could not read YAML file {yaml_filepath}: {e}")
            continue

    # If we get here, the daily report wasn't found in either directory
    print(f"Warning: Daily report YAML file not found for '{daily_report_name}' in prod or staging directories")
    return None


def validate_frequency_selection(form_data: FormData) -> tuple[bool, str]:
    """Validate that at least one delivery frequency is selected.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    if not form_data.daily_frequency_selected and not form_data.monthly_frequency_selected:
        return False, "At least one Delivery Frequency (Daily or Monthly) must be selected."
    return True, ""


def validate_report_name(form_data: FormData) -> tuple[bool, str]:
    """Validate report name based on selected frequencies.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    report_name_input = form_data.report_name_input
    daily_selected = form_data.daily_frequency_selected
    monthly_selected = form_data.monthly_frequency_selected
    both_selected = daily_selected and monthly_selected

    # If both frequencies are selected
    if both_selected:
        # Main report name must be for daily
        if not report_name_input.endswith(DAILY_REPORT_SUFFIX):
            error_msg = (
                f"When both Daily and Monthly frequencies are selected, Report Name must end with "
                f"'{DAILY_REPORT_SUFFIX}'."
            )
            return False, error_msg

        # Monthly report name must be provided and end with monthly suffix
        monthly_report_name = form_data.monthly_report_name
        if not monthly_report_name:
            return False, "Monthly Report Name must be provided when both frequencies are selected."
        if not monthly_report_name.endswith(MONTHLY_REPORT_SUFFIX):
            error_msg = f"Monthly Report Name must end with '{MONTHLY_REPORT_SUFFIX}'."
            return False, error_msg
    # If only daily is selected, the input report name must be for daily
    elif daily_selected:
        if not report_name_input.endswith(DAILY_REPORT_SUFFIX):
            error_msg = f"When Daily frequency is selected, Report Name must end with '{DAILY_REPORT_SUFFIX}'."
            return False, error_msg
    # If only monthly is selected, the input report name must be for monthly
    elif monthly_selected:
        if not report_name_input.endswith(MONTHLY_REPORT_SUFFIX):
            error_msg = f"When only Monthly frequency is selected, Report Name must end with '{MONTHLY_REPORT_SUFFIX}'."
            return False, error_msg

    return True, ""


def validate_required_fields(form_data: FormData) -> tuple[bool, str]:
    """Validate that all required fields are filled.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    required_fields = [
        ("app_name", "App Name"),
        ("report_name_input", "Report Name"),
        ("client_timezone", "Client Timezone"),
        ("remittance_type", "Remittance Type"),
        ("service_offering_type", "Service Offering Type"),
        ("soid", "SOID"),
    ]

    for field_name, display_name in required_fields:
        if not getattr(form_data, field_name):
            return False, f"{display_name} is required."

    return True, ""


def validate_delivery_config(form_data: FormData) -> tuple[bool, str]:
    """Validate delivery configuration.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    # Check if at least one delivery method is selected
    if not form_data.delivery_method_str:
        return False, "At least one Delivery Config (Email or FTP) must be selected."

    # Check for FTP path when FTP is selected
    if form_data.ftp_selected and not form_data.ftp_path_val:
        return False, "FTP Filepath must be provided when FTP delivery is selected."

    # Check for email subject when Email is selected
    if form_data.email_selected and not form_data.email_subject_val:
        return False, "Email Subject must be provided when Email delivery is selected."

    return True, ""


def validate_addons(form_data: FormData) -> tuple[bool, str]:
    """Validate Add-Ons selection.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    # Add-ons are optional, so no validation needed
    return True, ""


def validate_email_subject(form_data: FormData) -> tuple[bool, str]:
    """Validate email subject based on frequency selection.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    if form_data.email_selected and form_data.email_subject_val:
        # For both frequencies or daily only: must end with "Remittance DATE"
        if form_data.daily_frequency_selected:
            if not form_data.email_subject_val.endswith("Remittance DATE"):
                error_msg = "Email Subject must end with 'Remittance DATE' when Daily frequency is selected."
                return False, error_msg
        # For monthly only: must end with "Monthly Recap DATE"
        elif form_data.monthly_frequency_selected:
            if not form_data.email_subject_val.endswith("Monthly Recap DATE"):
                error_msg = "Email Subject must end with 'Monthly Recap DATE' when only Monthly is selected."
                return False, error_msg

    return True, ""


def validate_email_base(form_data: FormData) -> tuple[bool, str]:
    """Validate email base when manual input is selected.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    if not form_data.is_auto_email_base and not form_data.email_base_val:
        return False, "Email Base Name cannot be empty when Auto Generate is unchecked."
    return True, ""


def validate_manual_additional_fields(form_data: FormData) -> tuple[bool, str]:
    """Validate manual additional fields for duplicates and completeness.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    if not form_data.manual_additional_fields:
        return True, ""

    # Check for empty name or source fields
    for i, field in enumerate(form_data.manual_additional_fields):
        if not field.get("name", "").strip():
            return False, f"Additional field #{i + 1}: Name cannot be empty."
        if not field.get("source", "").strip():
            return False, f"Additional field #{i + 1}: Source cannot be empty."

    # Check for duplicate field names (case-insensitive)
    field_names = [field["name"].lower().strip() for field in form_data.manual_additional_fields]
    if len(field_names) != len(set(field_names)):
        return False, "Duplicate field names found in additional fields. Each field name must be unique."

    # Check for duplicate source names (case-insensitive)
    field_sources = [field["source"].lower().strip() for field in form_data.manual_additional_fields]
    if len(field_sources) != len(set(field_sources)):
        return False, "Duplicate source names found in additional fields. Each source must be unique."

    return True, ""


def validate_schedule_timing(form_data: FormData) -> tuple[bool, str]:
    """Validate schedule conflicts and timing.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    # Collect schedule times based on selected frequencies
    schedule_times = []

    if form_data.daily_frequency_selected:
        daily_time = form_data.daily_schedule_time
        schedule_times.append((daily_time.hour(), daily_time.minute()))

    if form_data.monthly_frequency_selected:
        monthly_time = form_data.monthly_schedule_time
        schedule_times.append((monthly_time.hour(), monthly_time.minute()))

    if not schedule_times:
        return True, ""

    # Validate the schedule times for conflicts
    try:
        validation_result = validate_schedule_conflicts(schedule_times)
        if not validation_result.is_valid:
            return False, validation_result.error_message
    except Exception as e:
        # If schedule validation fails for any reason, log a warning but don't block creation
        print(f"Warning: Could not validate schedule conflicts: {e}")

    # Validate dual frequency schedule timing (monthly must be 10+ minutes after daily)
    if form_data.daily_frequency_selected and form_data.monthly_frequency_selected:
        is_valid, error_msg = validate_dual_frequency_timing(
            form_data.daily_schedule_time, form_data.monthly_schedule_time
        )
        if not is_valid:
            return False, error_msg

    return True, ""


def validate_monthly_specific_fields(form_data: FormData) -> tuple[bool, str]:
    """Validate monthly-specific fields.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    # Validate date_with_transactions when monthly is selected
    if form_data.monthly_frequency_selected and not form_data.date_with_transactions:
        return False, "Date With Transactions must be provided for monthly reports."

    # Validate daily_report_name when only monthly is selected
    if form_data.monthly_frequency_selected and not form_data.daily_frequency_selected:
        if not form_data.daily_report_name:
            return False, "Daily Report Name must be provided when only Monthly frequency is selected."

    # Validate monthly_report_name when both frequencies are selected
    if form_data.daily_frequency_selected and form_data.monthly_frequency_selected:
        if not form_data.monthly_report_name:
            return False, "Monthly Report Name must be provided when both frequencies are selected."

    return True, ""


def validate_monthly_scheduling(form_data: FormData) -> tuple[bool, str]:
    """Validate monthly scheduling to prevent conflicts.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    if not form_data.monthly_frequency_selected:
        return True, ""

    monthly_schedule_time = form_data.monthly_schedule_time

    # When only monthly is selected, validate against the specific daily report
    if not form_data.daily_frequency_selected and form_data.daily_report_name:
        daily_schedule_time = _get_daily_report_schedule_time(form_data.daily_report_name)
        if daily_schedule_time:
            is_valid, error_msg = validate_dual_frequency_timing(daily_schedule_time, monthly_schedule_time)
            if not is_valid:
                # Customize the error message for monthly-only case
                error_msg = error_msg.replace(
                    "Monthly schedule time must be at least 10 minutes after daily schedule time.",
                    f"Monthly schedule time must be at least 10 minutes after: '{form_data.daily_report_name}'",
                )
                return False, error_msg

    # Additional validation for monthly on day 1 using SOID-based search (existing logic)
    monthly_day_of_month_val = form_data.monthly_day_of_month_val
    soid = form_data.soid

    conflicting_daily_times = find_conflicting_daily_schedules(soid)
    if monthly_day_of_month_val == 1 and conflicting_daily_times:
        latest_daily_time = QTime(0, 0)
        valid_times_found = False
        for cron_str in conflicting_daily_times:
            parts = cron_str.split()
            if len(parts) >= 2:
                try:
                    minute, hour = int(parts[0]), int(parts[1])
                    daily_time = QTime(hour, minute)
                    if daily_time > latest_daily_time:
                        latest_daily_time = daily_time
                    valid_times_found = True
                except ValueError:
                    print(f"Warning: Could not parse time from cron: {cron_str}")

        if valid_times_found:
            min_allowed_time = latest_daily_time.addSecs(600)  # Add 10 minutes
            if monthly_schedule_time < min_allowed_time:
                error_msg = (
                    f"Monthly report on day 1 must be scheduled at least 10 mins "
                    f"after the latest daily report for SOID {soid}. "
                    f"Latest daily: {latest_daily_time.toString('HH:mm')}. "
                    f"Min allowed for monthly: {min_allowed_time.toString('HH:mm')}."
                )
                return False, error_msg

    return True, ""


def validate_all_inputs(form_data: FormData) -> tuple[bool, str]:
    """Validate all input fields.

    Args:
        form_data (FormData): The form data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    # List of validation functions to run
    validators = [
        validate_frequency_selection,
        validate_report_name,
        validate_required_fields,
        validate_delivery_config,
        validate_addons,
        validate_email_subject,
        validate_email_base,
        validate_manual_additional_fields,
        validate_schedule_timing,
        validate_monthly_specific_fields,
        validate_monthly_scheduling,
    ]

    # Run all validators
    for validator in validators:
        is_valid, error_message = validator(form_data)
        if not is_valid:
            return False, error_message

    return True, ""


def validate_run_report_inputs(run_data: RunReportData) -> tuple[bool, str]:
    """Validate run report inputs.

    Args:
        run_data (RunReportData): The run report data to validate.

    Returns:
        tuple[bool, str]: (is_valid, error_message)
    """
    if not run_data.report_name:
        return False, "Please specify a report name."

    if not run_data.report_dates:
        return False, "Please select at least one report date."

    if run_data.environment not in ["dev", "staging"]:
        return False, "Invalid environment selected. Must be 'dev' or 'staging'."

    return True, ""
