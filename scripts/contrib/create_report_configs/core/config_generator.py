"""Main configuration generator orchestrator."""

from pathlib import Path
from typing import Callable

from scripts.contrib.create_report_configs.core.json_generator import (
    create_daily_json_config,
    create_monthly_json_config,
)
from scripts.contrib.create_report_configs.core.yaml_generator import create_scheduled_job_config
from scripts.contrib.create_report_configs.integrations.custom_integration import generate_custom_integration_module
from scripts.contrib.create_report_configs.integrations.wrapper_manager import update_remittance_report_wrapper
from scripts.contrib.create_report_configs.models.constants import (
    CUSTOM_INTEGRATION_DIR,
    JSON_CONFIG_DIR,
    PROD_SCHEDULED_JOB_DIR,
    PROJECT_ROOT,
    STAGING_SCHEDULED_JOB_DIR,
)
from scripts.contrib.create_report_configs.models.report_config import FormData, ReportConfig
from scripts.contrib.create_report_configs.utils.file_operations import (
    check_existing_files,
    read_text_file,
    write_json_file,
    write_text_file,
    write_yaml_file,
)
from scripts.contrib.create_report_configs.utils.scheduling import format_cron_schedule, validate_cron_schedule


class ConfigGenerator:
    """Main orchestrator for generating report configurations."""

    def __init__(self, log_callback: Callable[[str, str], None] | None = None):
        """Initialize the ConfigGenerator.

        Args:
            log_callback (Callable[[str, str], None] | None): Optional logging callback function.
        """
        self.log_callback = log_callback

    def _log(self, message: str, color: str = "black") -> None:
        """Log a message using the callback or print to console.

        Args:
            message (str): The message to log.
            color (str): The color for the message.
        """
        if self.log_callback:
            self.log_callback(message, color)
        else:
            print(f"[{color.upper()}] {message}")

    def generate_configuration_files(
        self,
        form_data: FormData,
        frequency: str,
        overwrite_existing: bool = False,
    ) -> tuple[bool, str]:
        """Generate and write JSON and YAML configuration files.

        Orchestrates the file generation process. It checks for existing files
        (unless overwrite is True), calls the appropriate config generators,
        and writes the generated content to the appropriate files,
        and returns a status tuple.

        For monthly reports, it also generates a custom integration .py file and updates
        the CUSTOM_INTEGRATION_MAP and imports in remittance_report_wrapper.py.

        Args:
            form_data (FormData): The validated form data.
            frequency (str): The frequency ("daily" or "monthly").
            overwrite_existing (bool): Whether to overwrite existing files.

        Returns:
            tuple[bool, str]: A tuple containing:
                - bool: True if file generation was successful, False otherwise.
                - str: A message describing the outcome (success message with file
                  list or an error message).
        """
        try:
            # Create the report config from form data
            report_config = self._create_report_config(form_data, frequency)

            # --- Derive filenames ---
            json_filename = f"{report_config.report_name}.json"
            json_filepath = JSON_CONFIG_DIR / json_filename

            # --- Check for existing files ---
            existing_files = self._check_existing_files(report_config, json_filepath, form_data.create_json_only)

            # Only check for existing files if overwrite is not enabled
            if existing_files and not overwrite_existing:
                file_list = "\n  ".join([str(f.relative_to(PROJECT_ROOT)) for f in existing_files])
                error_message = f"Operation aborted. The following file(s) already exist:\n  {file_list}"
                return False, error_message

            # --- Generate and write JSON configuration ---
            success, message = self._process_json_config(report_config, json_filepath, form_data)
            if not success:
                return False, message

            success_files = [json_filepath.name]

            # --- Generate and write YAML configurations if not JSON-only ---
            if not form_data.create_json_only:
                yaml_success, yaml_files = self._process_yaml_configs(report_config)
                if not yaml_success:
                    return False, "Failed to generate YAML configurations"
                success_files.extend(yaml_files)

            # --- Handle custom integration for monthly reports ---
            if frequency == "monthly":
                integration_success, integration_files = self._process_monthly_integration(
                    report_config, overwrite_existing
                )
                if not integration_success:
                    return False, "Failed to process monthly integration"
                success_files.extend(integration_files)

            # --- Return success ---
            file_list_str = "\n  ".join(success_files)
            success_message = f"Successfully created/updated:\n  {file_list_str}"
            return True, success_message

        except Exception as e:
            error_message = f"An unexpected error occurred: {e}"
            self._log(error_message, "red")
            return False, error_message

    def _create_report_config(self, form_data: FormData, frequency: str) -> ReportConfig:
        """Create a ReportConfig from FormData.

        Args:
            form_data (FormData): The form data.
            frequency (str): The frequency.

        Returns:
            ReportConfig: The report configuration.
        """
        # Create cron schedule
        if frequency == "daily":
            schedule_time = form_data.daily_schedule_time
            day_of_month = None
        else:  # monthly
            schedule_time = form_data.monthly_schedule_time
            day_of_month = form_data.monthly_day_of_month_val

        cron_schedule = format_cron_schedule(schedule_time, frequency, day_of_month)

        # Validate cron schedule
        is_valid, error_msg = validate_cron_schedule(cron_schedule)
        if not is_valid:
            raise ValueError(f"{frequency.title()} cron schedule '{cron_schedule}' is invalid: {error_msg}")

        return ReportConfig(
            app_name=form_data.app_name,
            report_name=form_data.report_name,
            client_timezone=form_data.client_timezone,
            remittance_type=form_data.remittance_type,
            service_offering_type=form_data.service_offering_type,
            soid=form_data.soid,
            delivery_method=form_data.delivery_method_str,
            frequency=frequency,
            cron_schedule=cron_schedule,
            ftp_filepath=form_data.ftp_path_val,
            email_subject=form_data.daily_email_subject if frequency == "daily" else form_data.monthly_email_subject,
            addons=form_data.addons,
            advanced_selections=form_data.advanced_selections_val,
            manual_email_base=form_data.email_base_val,
            manual_additional_fields=form_data.manual_additional_fields,
            ftp_credential=form_data.ftp_credential_val,
            daily_report_name=form_data.daily_report_name,
            date_with_transactions=form_data.date_with_transactions,
        )

    def _check_existing_files(
        self, report_config: ReportConfig, json_filepath: Path, create_json_only: bool
    ) -> list[Path]:
        """Check for existing files that would be created.

        Args:
            report_config (ReportConfig): The report configuration.
            json_filepath (Path): Path to the JSON file.
            create_json_only (bool): Whether only creating JSON files.

        Returns:
            list[Path]: List of existing file paths.
        """
        staging_yaml_filepath = None
        prod_yaml_filepath = None
        custom_py_filepath = None

        if not create_json_only:
            yaml_suffix = f"{report_config.frequency}_client.yaml"
            staging_yaml_filename = f"{report_config.report_name}_{yaml_suffix}"
            prod_yaml_filename = f"{report_config.report_name}_{yaml_suffix}"
            staging_yaml_filepath = STAGING_SCHEDULED_JOB_DIR / staging_yaml_filename
            prod_yaml_filepath = PROD_SCHEDULED_JOB_DIR / prod_yaml_filename

        if report_config.frequency == "monthly":
            custom_py_filename = f"{report_config.report_name}.py"
            custom_py_filepath = CUSTOM_INTEGRATION_DIR / custom_py_filename

        return check_existing_files(json_filepath, staging_yaml_filepath, prod_yaml_filepath, custom_py_filepath)

    def _process_json_config(
        self, report_config: ReportConfig, json_filepath: Path, form_data: FormData
    ) -> tuple[bool, str]:
        """Process JSON configuration generation.

        Args:
            report_config (ReportConfig): The report configuration.
            json_filepath (Path): Path to write the JSON file.
            form_data (FormData): The original form data.

        Returns:
            tuple[bool, str]: (success, message)
        """
        try:
            if report_config.frequency == "monthly":
                # Generate custom integration module for monthly reports
                custom_module_dict, _, _, _ = generate_custom_integration_module(
                    report_config.report_name,
                    report_config.soid,
                    False,  # overwrite check will be handled separately
                    report_config.daily_report_name,
                    report_config.date_with_transactions,
                    report_config.advanced_selections,
                    report_config.remittance_type,
                )

                json_content = create_monthly_json_config(
                    app_name=report_config.app_name,
                    report_name=report_config.report_name,
                    soid=report_config.soid,
                    manual_email_base=report_config.manual_email_base,
                    email_subject_from_gui=report_config.email_subject,
                    delivery_method_for_monthly=report_config.delivery_method,
                    service_offering_type_from_gui=report_config.service_offering_type,
                    custom_integration_module_dict=custom_module_dict,
                    ftp_filepath=report_config.ftp_filepath,
                    manual_additional_fields=report_config.manual_additional_fields,
                )
            else:  # daily
                json_content = create_daily_json_config(
                    app_name=report_config.app_name,
                    report_name=report_config.report_name,
                    client_timezone=report_config.client_timezone,
                    remittance_type=report_config.remittance_type,
                    service_offering_type=report_config.service_offering_type,
                    soid=report_config.soid,
                    delivery_method=report_config.delivery_method,
                    ftp_filepath=report_config.ftp_filepath,
                    email_subject=report_config.email_subject,
                    addons=report_config.addons,
                    advanced_selections=report_config.advanced_selections,
                    manual_email_base=report_config.manual_email_base,
                    manual_additional_fields=report_config.manual_additional_fields,
                )

            # Write JSON file
            write_json_file(json_filepath, json_content)
            self._log(f"Successfully created JSON config: {json_filepath.relative_to(PROJECT_ROOT)}", "green")
            return True, "JSON config created successfully"

        except Exception as e:
            error_msg = f"Failed to create JSON config: {e}"
            self._log(error_msg, "red")
            return False, error_msg

    def _process_yaml_configs(self, report_config: ReportConfig) -> tuple[bool, list[str]]:
        """Process YAML configuration generation.

        Args:
            report_config (ReportConfig): The report configuration.

        Returns:
            tuple[bool, list[str]]: (success, list_of_created_files)
        """
        try:
            yaml_suffix = f"{report_config.frequency}_client.yaml"
            staging_yaml_filename = f"{report_config.report_name}_{yaml_suffix}"
            prod_yaml_filename = f"{report_config.report_name}_{yaml_suffix}"
            staging_yaml_filepath = STAGING_SCHEDULED_JOB_DIR / staging_yaml_filename
            prod_yaml_filepath = PROD_SCHEDULED_JOB_DIR / prod_yaml_filename

            # Generate YAML configurations
            staging_yaml_content = create_scheduled_job_config(
                report_base_name=report_config.report_name,
                cron_schedule=report_config.cron_schedule,
                frequency=report_config.frequency,
                env="staging",
                service_offering_type=report_config.service_offering_type,
                ftp_credential=report_config.ftp_credential,
            )

            prod_yaml_content = create_scheduled_job_config(
                report_base_name=report_config.report_name,
                cron_schedule=report_config.cron_schedule,
                frequency=report_config.frequency,
                env="prod",
                service_offering_type=report_config.service_offering_type,
                ftp_credential=report_config.ftp_credential,
            )

            # Write YAML files
            write_yaml_file(staging_yaml_filepath, staging_yaml_content)
            write_yaml_file(prod_yaml_filepath, prod_yaml_content)

            self._log(f"Successfully created Staging YAML: {staging_yaml_filepath.relative_to(PROJECT_ROOT)}", "green")
            self._log(f"Successfully created Prod YAML: {prod_yaml_filepath.relative_to(PROJECT_ROOT)}", "green")

            return True, [staging_yaml_filepath.name, prod_yaml_filepath.name]

        except Exception as e:
            error_msg = f"Failed to create YAML configs: {e}"
            self._log(error_msg, "red")
            return False, []

    def _process_monthly_integration(
        self, report_config: ReportConfig, overwrite_existing: bool
    ) -> tuple[bool, list[str]]:
        """Process monthly integration file generation and wrapper updates.

        Args:
            report_config (ReportConfig): The report configuration.
            overwrite_existing (bool): Whether to overwrite existing files.

        Returns:
            tuple[bool, list[str]]: (success, list_of_created_files)
        """
        try:
            created_files = []

            # Generate custom integration module
            custom_module_dict, py_file_edit_params, class_name_for_wrapper, module_path_for_wrapper = (
                generate_custom_integration_module(
                    report_config.report_name,
                    report_config.soid,
                    overwrite_existing,
                    report_config.daily_report_name,
                    report_config.date_with_transactions,
                    report_config.advanced_selections,
                    report_config.remittance_type,
                )
            )

            # Create custom .py file if needed
            if py_file_edit_params:
                try:
                    py_target_path = Path(py_file_edit_params["target_file"])
                    CUSTOM_INTEGRATION_DIR.mkdir(parents=True, exist_ok=True)

                    # Check for overwrite for .py file specifically
                    if py_target_path.exists() and not overwrite_existing:
                        error_msg = (
                            f"Error: Custom integration file "
                            f"{py_target_path.relative_to(PROJECT_ROOT)} already exists. "
                            "Overwrite is disabled."
                        )
                        self._log(error_msg, "red")
                        return False, []
                    else:
                        write_text_file(py_target_path, py_file_edit_params["code_edit"])
                        self._log(
                            f"Success: Created custom py file: {py_target_path.relative_to(PROJECT_ROOT)}", "green"
                        )
                        created_files.append(py_target_path.name)

                except IOError:
                    return False, []

            # Update remittance_report_wrapper.py
            if class_name_for_wrapper and module_path_for_wrapper:
                try:
                    from scripts.contrib.create_report_configs.models.constants import WRAPPER_FILE_PATH

                    if not WRAPPER_FILE_PATH.exists():
                        self._log(f"Error: Wrapper file {WRAPPER_FILE_PATH} not found. Cannot update map.", "red")
                    else:
                        current_wrapper_content = read_text_file(WRAPPER_FILE_PATH)

                        wrapper_update_params = update_remittance_report_wrapper(
                            class_name=class_name_for_wrapper,
                            module_path=module_path_for_wrapper,
                            current_wrapper_content=current_wrapper_content,
                            report_name=report_config.report_name,
                        )

                        if wrapper_update_params:
                            try:
                                write_text_file(
                                    Path(wrapper_update_params["target_file"]), wrapper_update_params["code_edit"]
                                )
                                self._log(f"Success: {wrapper_update_params['instructions']}", "green")
                            except IOError as e:
                                self._log(f"Error: Could not write updated wrapper file: {e}", "red")
                        else:
                            self._log(
                                "Info: CUSTOM_INTEGRATION_MAP already sorted and contains entry. No update needed.",
                                "blue",
                            )

                except Exception as e:
                    self._log(f"An unexpected error occurred during wrapper update process: {e}", "red")

            return True, created_files

        except Exception as e:
            error_msg = f"Failed to process monthly integration: {e}"
            self._log(error_msg, "red")
            return False, []
