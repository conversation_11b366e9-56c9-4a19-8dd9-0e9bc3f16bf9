"""Report execution core logic."""

from typing import Callable

from PySide6.QtCore import QProcess

from scripts.contrib.create_report_configs.models.constants import PROJECT_ROOT
from scripts.contrib.create_report_configs.models.report_config import RunReportData
from scripts.contrib.create_report_configs.utils.environment import read_env_variable


class ReportRunner:
    """Handles the execution of reports with proper separation from GUI concerns."""

    def __init__(self, log_callback: Callable[[str, str], None] | None = None):
        """Initialize the ReportRunner.

        Args:
            log_callback (Callable[[str, str], None] | None): Optional logging callback function.
        """
        self.log_callback = log_callback
        self.process: QProcess | None = None
        self.is_running = False

    def _log(self, message: str, color: str = "black") -> None:
        """Log a message using the callback or print to console.

        Args:
            message (str): The message to log.
            color (str): The color for the message.
        """
        if self.log_callback:
            self.log_callback(message, color)
        else:
            print(f"[{color.upper()}] {message}")

    def _is_monthly_report(self, report_name: str) -> bool:
        """Check if the given report name indicates a monthly report.

        Args:
            report_name (str): The name of the report to check.

        Returns:
            bool: True if the report is a monthly report, False otherwise.
        """
        return "monthly" in report_name.lower()

    def prepare_report_execution(self, run_data: RunReportData) -> tuple[bool, str, dict[str, str]]:
        """Prepare the environment and command for report execution.

        Args:
            run_data (RunReportData): The run report data containing report name, date, and environment.

        Returns:
            tuple[bool, str, dict[str, str]]: (success, error_message, env_vars)
        """
        # Check if this is a monthly report
        is_monthly_report = self._is_monthly_report(run_data.report_name)

        if is_monthly_report:
            # Monthly reports don't use Snowflake
            env_vars = {
                "ENV_FOR_DYNACONF": run_data.environment,
                "FR_BASE_NAME": "financial_remittance",
                "FR_REPORT_NAME": run_data.report_name,
                "FR_ENABLE_DELIVERY": "false",
                "FR_SAVE_REMITTANCE_DATA": "false",
                "FR_LOCAL_RUN": "true",
                "FR_USE_MONGO_DATA": "true",
                "FR_REPORT_DATE": run_data.report_date,
            }
        else:
            # Read required environment variable for daily reports
            username = read_env_variable("FR_SNOWFLAKE_OKTA_USERNAME")
            if not username:
                return False, "FR_SNOWFLAKE_OKTA_USERNAME not found in .env file.", {}

            # Build the environment variables for daily reports
            env_vars = {
                "ENV_FOR_DYNACONF": run_data.environment,
                "FR_BASE_NAME": "financial_remittance",
                "FR_REPORT_NAME": run_data.report_name,
                "FR_ENABLE_DELIVERY": "false",
                "FR_SAVE_REMITTANCE_DATA": "false",
                "FR_LOCAL_RUN": "true",
                "FR_REPORT_DATE": run_data.report_date,
                "FR_USE_SNOWFLAKE": "true",
                "FR_SNOWFLAKE_OKTA_USERNAME": username,
            }

        return True, "", env_vars

    def build_command(self, env_vars: dict[str, str]) -> str:
        """Build the command string with environment variables.

        Args:
            env_vars (dict[str, str]): Environment variables to include.

        Returns:
            str: The complete command string.
        """
        # Check if this is a monthly report (no Snowflake variables)
        is_monthly_report = "FR_USE_SNOWFLAKE" not in env_vars

        if is_monthly_report:
            # For monthly reports, we need to unset AWS variables and set AWS_PROFILE
            # Build the environment variables string
            env_string = " ".join([f"{key}={value}" for key, value in env_vars.items()])

            # Construct the full command with proper shell syntax
            command = (
                "unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN; "
                "export AWS_PROFILE=staging_eks; "
                f"{env_string} poetry run python entrypoint.py"
            )

            self._log(f"Building monthly report command: {command}", "blue")
            return command
        else:
            # For daily reports, use the standard format
            env_string = " ".join([f"{key}={value}" for key, value in env_vars.items()])
            command = f"{env_string} poetry run python entrypoint.py"

            self._log(f"Building daily report command: {command}", "blue")
            return command

    def start_report_execution(
        self,
        command: str,
        stdout_callback: Callable[[], None],
        stderr_callback: Callable[[], None],
        finished_callback: Callable[[int], None],
    ) -> tuple[bool, str]:
        """Start the report execution process.

        Args:
            command (str): The command to execute.
            stdout_callback (Callable[[], None]): Callback for stdout handling.
            stderr_callback (Callable[[], None]): Callback for stderr handling.
            finished_callback (Callable[[int], None]): Callback for process completion.

        Returns:
            tuple[bool, str]: (success, error_message)
        """
        if self.is_running:
            return False, "Report is already running. Please wait for it to complete."

        try:
            self._log(f"Starting report execution with command: {command}", "yellow")

            # Create QProcess for better Qt integration
            self.process = QProcess()
            self.process.setWorkingDirectory(str(PROJECT_ROOT))

            self._log(f"Working directory set to: {PROJECT_ROOT}", "gray")

            # Connect signals for output handling
            self.process.readyReadStandardOutput.connect(stdout_callback)
            self.process.readyReadStandardError.connect(stderr_callback)
            self.process.finished.connect(finished_callback)

            # Start the process
            self.process.start("bash", ["-c", command])

            if not self.process.waitForStarted(3000):
                self.is_running = False
                error_msg = f"Failed to start report process. Error: {self.process.errorString()}"
                self._log(error_msg, "red")
                return False, error_msg
            else:
                self.is_running = True
                self._log("Report process started successfully", "green")
                return True, ""

        except Exception as e:
            self.is_running = False
            error_msg = f"Failed to start report execution: {e}"
            self._log(error_msg, "red")
            return False, error_msg

    def set_running_state(self, is_running: bool) -> None:
        """Set the running state of the report runner.

        Args:
            is_running (bool): Whether the report is currently running.
        """
        self.is_running = is_running

    def get_process_output(self, output_type: str) -> str:
        """Get output from the running process.

        Args:
            output_type (str): Type of output to get ('stdout' or 'stderr').

        Returns:
            str: The decoded output text.
        """
        if not self.process:
            return ""

        if output_type == "stdout":
            data = self.process.readAllStandardOutput()
        elif output_type == "stderr":
            data = self.process.readAllStandardError()
        else:
            return ""

        # Handle different return types from data.data()
        raw_data = data.data()
        if isinstance(raw_data, bytes):
            return raw_data.decode()
        elif isinstance(raw_data, bytearray):
            return raw_data.decode()
        elif hasattr(raw_data, "tobytes"):
            return raw_data.tobytes().decode()
        else:
            return str(raw_data)
