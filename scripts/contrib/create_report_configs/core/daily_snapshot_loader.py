"""Load key data from an existing *daily* JSON report configuration.

The goal is to provide the GUI with a small, typed ``DailyReportSnapshot`` instance that can be applied to the
Monthly-report form.  All parsing and inference logic lives here; the GUI layer should do *zero* JSON inspection.
"""

from __future__ import annotations

import json
from typing import Any, Literal, cast

from scripts.contrib.create_report_configs.models.constants import JSON_CONFIG_DIR
from scripts.contrib.create_report_configs.models.daily_report_snapshot import DailyReportSnapshot

__all__ = ["ConfigNotFoundError", "InvalidConfigError", "load_daily_snapshot"]


class ConfigNotFoundError(FileNotFoundError):
    """Raised when the requested report JSON config does not exist."""


class InvalidConfigError(ValueError):
    """Raised when the JSON config is malformed or missing required keys."""


_GUI_TO_JSON_KEY_MAP = {
    "Cancellation": "cancellation",
    "Chargeback": "chargeback",
    "Failure": "failure",
    "Refund": "refund",
    "Same Day Cancellation": "same_day_cancellation",
    "Same Day Failure": "same_day_failure",
    "Same Day Refund": "same_day_refund",
    "Success": "success",
}
_JSON_TO_GUI_KEY_MAP = {v: k for k, v in _GUI_TO_JSON_KEY_MAP.items()}

# Mapping of model identifier → Service Offering label (duplicated logic previously in GUI).
_MODEL_TO_SERVICE_TYPE = {
    "property_tax_service": "Property Tax",
    "citations": "Citations",
    "payit_forms": "Non-Integrated Form",
    "pos_service": "Non-Integrated POS",
    "checkout_reconciliation": "SDK",
    "water_service": "Water",
    "verticalless": "Verticalless",
}


def _derive_remittance_behaviour(
    statuses: dict[str, list[str]],
) -> tuple[Literal["Combined", "Return"], dict[str, str] | None]:
    """Figure out the high-level remittance type and any advanced overrides."""

    combined_pattern = all(v == ["COMBINED"] for v in statuses.values())

    return_pattern = (
        statuses.get("success") == ["SUCCESS"]
        and all(
            statuses[k] == ["RETURN"]
            for k in [
                "cancellation",
                "chargeback",
                "failure",
                "refund",
            ]
        )
        and all(
            statuses[k] == ["SUCCESS", "RETURN"]
            for k in [
                "same_day_cancellation",
                "same_day_failure",
                "same_day_refund",
            ]
        )
    )

    if combined_pattern:
        return "Combined", None
    if return_pattern:
        return "Return", None

    # --- Custom mix: build GUI advanced selections dict ---
    # Map list→ label ; reference reverse_status_map from old impl
    reverse_status_map = {
        ("COMBINED",): "COMBINED",
        ("RETURN",): "RETURN",
        ("SUCCESS",): "SUCCESS",
        ("SUCCESS", "RETURN"): "SUCCESS/RETURN",
        ("RETURN", "SUCCESS"): "SUCCESS/RETURN",
    }

    adv: dict[str, str] = {}
    for json_key, gui_key in _JSON_TO_GUI_KEY_MAP.items():
        status_list = tuple(statuses.get(json_key, []))
        label = reverse_status_map.get(status_list, "default")
        adv[gui_key] = label

    # Base type: if success list contains COMBINED then Combined else Return.
    base_type: Literal["Combined", "Return"] = "Combined" if "COMBINED" in statuses.get("success", []) else "Return"
    return base_type, adv


def _map_service_offering(data_sources: dict[str, Any] | None) -> str:
    """Convert snowpaw model list → service offering label.

    Iterates through the list of models in reverse order and returns the first supported service type found.
    Raises an error if none are supported.
    """
    if not data_sources:
        raise InvalidConfigError("'data_sources' missing from JSON config.")

    models = cast(list[dict[str, Any]], data_sources.get("snowpaw_remittance_models", []))
    if not models:
        raise InvalidConfigError("'snowpaw_remittance_models' list empty in JSON config.")

    for model in reversed(models):
        model_name = model.get("model_name", "")
        service_type = _MODEL_TO_SERVICE_TYPE.get(model_name)
        if service_type:
            return service_type
    # If none of the model_names are supported, raise the error for the last one
    last_model_name = models[-1].get("model_name", "") if models else ""
    raise InvalidConfigError(f"Unsupported model_name '{last_model_name}' in JSON config.")


_STATUS_KEYS = [
    "cancellation",
    "chargeback",
    "failure",
    "refund",
    "same_day_cancellation",
    "same_day_failure",
    "same_day_refund",
    "success",
]


def load_daily_snapshot(report_name: str) -> DailyReportSnapshot:
    """Parse *report_name*.json and return a :class:`DailyReportSnapshot`.

    Args:
        report_name: Stem of the JSON file (no ``.json`` extension).

    Raises:
        ConfigNotFoundError: if the JSON config file does not exist.
        InvalidConfigError: if required information is missing or malformed.
    """
    cfg_path = JSON_CONFIG_DIR / f"{report_name}.json"
    if not cfg_path.exists():
        raise ConfigNotFoundError(str(cfg_path))

    try:
        with open(cfg_path, "r", encoding="utf-8") as jf:
            cfg = json.load(jf)
    except Exception as exc:
        raise InvalidConfigError(f"Failed to read JSON config: {exc}") from exc

    # Basic
    app_name_list = cfg.get("app_names", [])
    if not app_name_list:
        raise InvalidConfigError("'app_names' missing or empty in JSON config.")
    app_name = cast(str, app_name_list[0])

    soid_list = cfg.get("service_offering_ids", [])
    if not soid_list:
        raise InvalidConfigError("'service_offering_ids' missing or empty in JSON config.")
    soid = cast(str, soid_list[0])

    client_tz = cast(str, cfg.get("client_timezone", ""))
    if not client_tz:
        raise InvalidConfigError("'client_timezone' missing in JSON config.")

    # Delivery
    delivery_cfg = cast(dict[str, Any], cfg.get("delivery_configs", {}))
    email_cfg = cast(dict[str, Any] | None, delivery_cfg.get("email"))
    ftp_cfg = cast(dict[str, Any] | None, delivery_cfg.get("ftp"))

    email_selected = email_cfg is not None
    ftp_selected = ftp_cfg is not None

    email_subject = cast(str | None, email_cfg.get("subject")) if email_cfg else None
    ftp_filepath = cast(str | None, ftp_cfg.get("filepath")) if ftp_cfg else None

    email_base_val = cast(str | None, cfg.get("email_base"))

    output_file_name = cast(str | None, cfg.get("output_file_name"))

    # Service offering type
    service_type = _map_service_offering(cast(dict[str, Any], cfg.get("data_sources")))

    # Statuses → remittance info
    statuses: dict[str, list[str]] = {k: cast(list[str], cfg.get(k, [])) for k in _STATUS_KEYS}
    remittance_type, advanced_selections = _derive_remittance_behaviour(statuses)

    return DailyReportSnapshot(
        app_name=app_name,
        soid=soid,
        service_offering_type=service_type,
        client_timezone=client_tz,
        email_selected=email_selected,
        ftp_selected=ftp_selected,
        email_subject=email_subject,
        ftp_filepath=ftp_filepath,
        email_base=email_base_val,
        remittance_type=remittance_type,
        advanced_selections=advanced_selections,
        output_file_name=output_file_name,
    )
