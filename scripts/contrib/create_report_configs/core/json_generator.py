"""JSON configuration generators for reports."""

from typing import Any

from scripts.contrib.create_report_configs.models.service_configs import get_addon_additional_fields, get_service_config
from scripts.contrib.create_report_configs.utils.helpers import (
    generate_email_base,
    generate_output_filename,
    generate_report_config_tags,
)


def create_daily_json_config(
    app_name: str,
    report_name: str,
    client_timezone: str,
    remittance_type: str,
    service_offering_type: str,
    soid: str,
    delivery_method: str,
    ftp_filepath: str | None = None,
    email_subject: str | None = None,
    addons: list[str] | None = None,
    advanced_selections: dict[str, str] | None = None,
    manual_email_base: str | None = None,
    manual_additional_fields: list[dict[str, str]] | None = None,
) -> dict[str, Any]:
    """Generate the standard structure for the JSON config file.

    Constructs the JSON configuration dictionary based on the provided
    parameters, incorporating logic for default statuses, advanced overrides,
    service-specific settings, delivery configurations, and conditional fields.

    Args:
        app_name (str): The application name.
        report_name (str): The report name.
        client_timezone (str): The client timezone.
        remittance_type (str): The remittance type ("Return" or "Combined").
        service_offering_type (str): The service offering type.
        soid (str): The service offering ID.
        delivery_method (str): The delivery method ("email", "ftp", or "both").
        ftp_filepath (str | None): The FTP filepath if applicable.
        email_subject (str | None): The email subject if applicable.
        addons (list[str] | None): The selected add-ons if applicable.
        advanced_selections (dict[str, str] | None): Advanced status selections.
        manual_email_base (str | None): Manual email base if provided.
        manual_additional_fields (list[dict[str, str]] | None): Manual additional fields if provided.

    Returns:
        dict[str, Any]: The generated JSON configuration structure.

    Raises:
        ValueError: If an unsupported service offering type is encountered.
    """

    # --- Determine default report behavior based on remittance_type ---
    default_transaction_statuses = ["RETURN"] if remittance_type == "Return" else ["COMBINED"]
    default_same_day_statuses = ["SUCCESS", "RETURN"] if remittance_type == "Return" else ["COMBINED"]
    default_success_status = ["SUCCESS"] if remittance_type == "Return" else ["COMBINED"]

    # --- Initialize status dictionary with defaults ---
    current_statuses: dict[str, list[str]] = {
        "cancellation": default_transaction_statuses,
        "chargeback": default_transaction_statuses,
        "failure": default_transaction_statuses,
        "refund": default_transaction_statuses,
        "same_day_cancellation": default_same_day_statuses,
        "same_day_failure": default_same_day_statuses,
        "same_day_refund": default_same_day_statuses,
        "success": default_success_status,
    }

    # --- Override defaults if advanced selections are provided ---
    if advanced_selections:
        status_map: dict[str, list[str]] = {
            "COMBINED": ["COMBINED"],
            "RETURN": ["RETURN"],
            "SUCCESS": ["SUCCESS"],
            "SUCCESS/RETURN": ["SUCCESS", "RETURN"],
        }

        # Map GUI keys to JSON keys
        gui_to_json_key_map = {
            "Cancellation": "cancellation",
            "Chargeback": "chargeback",
            "Failure": "failure",
            "Refund": "refund",
            "Same Day Cancellation": "same_day_cancellation",
            "Same Day Failure": "same_day_failure",
            "Same Day Refund": "same_day_refund",
            "Success": "success",
        }

        for gui_key, json_key in gui_to_json_key_map.items():
            selected_value = advanced_selections.get(gui_key)
            if selected_value and selected_value != "default":
                current_statuses[json_key] = status_map.get(selected_value, current_statuses[json_key])

    # --- Get Service Offering Specific Configuration ---
    service_config = get_service_config(service_offering_type)

    # Extract values from the service configuration
    service_models: list[str] = service_config.model
    detail_sql_value: str = service_config.sql
    service_names: list[str] = service_config.service_names
    use_all_sdk_fields: bool = service_config.use_all_sdk_fields
    use_base_remittance_fields: bool = service_config.use_base_remittance_fields
    use_forms_data: bool = service_config.use_forms_data
    additional_fields: list[dict[str, str]] = service_config.additional_fields.copy()

    # --- No override needed for Non-Integrated POS as it now defaults to True ---

    # --- Build Delivery Configs ---
    delivery_configs: dict[str, dict[str, Any]] = {}
    derived_base = report_name.replace(f"{app_name}_", "").replace("_financial_remittance", "")

    # --- Determine Email Base ---
    final_email_base = generate_email_base(report_name, app_name, manual_email_base)

    # --- Tags generation ---
    tags_list = generate_report_config_tags(app_name, derived_base, "daily")

    if delivery_method in ["email", "both"]:
        email_config: dict[str, Any] = {}
        # Use provided subject, otherwise keep placeholder behavior
        email_config["subject"] = (
            email_subject if email_subject else f"{app_name} {derived_base.replace('_', ' ').title()} Remittance DATE"
        )
        email_config["tags"] = tags_list
        delivery_configs["email"] = email_config
    if delivery_method in ["ftp", "both"]:
        ftp_config: dict[str, str] = {}
        if ftp_filepath:
            ftp_config["filepath"] = ftp_filepath
        delivery_configs["ftp"] = ftp_config

    # --- Add Kafka delivery config (always included) ---
    delivery_configs["kafka"] = {"prosight": True}

    # --- Additional Fields for Add-ons ---
    if addons:
        additional_fields.extend(get_addon_additional_fields(addons))

    # --- Additional Fields for Manual ---
    if manual_additional_fields:
        additional_fields.extend(manual_additional_fields)

    # --- Construct the JSON structure ---
    snowpaw_models = [{"model_name": name} for name in service_models]
    output_filename = generate_output_filename(report_name, app_name)

    daily_json_structure: dict[str, Any] = {
        "additional_fields": additional_fields,
        "app_names": [app_name],
        "base_name": "financial_remittance",
        "cancellation": current_statuses["cancellation"],
        "chargeback": current_statuses["chargeback"],
        "client_timezone": client_timezone,
        "data_sources": {"snowpaw_remittance_models": snowpaw_models},
        "delivery_configs": delivery_configs,
        "detail_sql_file": detail_sql_value,
        "drop_rows_with_empty_columns": False,
        "email_base": final_email_base,
        "failure": current_statuses["failure"],
        "fields_to_decrypt": ["payer_first_name", "payer_last_name", "email_address"],
        "json_fields_to_parse": ["json_metadata_fields"],
        "output_file_name": output_filename,
        "prettify_sdk_values": True,
        "refund": current_statuses["refund"],
        "remove_duplicate_rows": True,
        "report_name": report_name,
        "same_day_cancellation": current_statuses["same_day_cancellation"],
        "same_day_failure": current_statuses["same_day_failure"],
        "same_day_refund": current_statuses["same_day_refund"],
        "service_names": service_names,
        "service_offering_ids": [soid],
        "success": current_statuses["success"],
        "summary": {
            "data": [
                {
                    "aggregation": "sum",
                    "field": "remittance_amount",
                    "format_type": "two_digit_precision",
                    "name": "Total Amount",
                },
                {"aggregation": "count", "field": "transaction_id", "name": "Number of Transactions"},
            ],
            "extra_rows": 1,
            "filters": [],
        },
        "use_all_sdk_fields": use_all_sdk_fields,
        "use_base_remittance_fields": use_base_remittance_fields,
        "use_forms_data": use_forms_data,
    }

    return daily_json_structure


def create_monthly_json_config(
    app_name: str,
    report_name: str,
    soid: str,
    manual_email_base: str | None = None,
    email_subject_from_gui: str | None = None,
    delivery_method_for_monthly: str | None = None,
    service_offering_type_from_gui: str | None = None,
    custom_integration_module_dict: dict[str, str] | None = None,
    ftp_filepath: str | None = None,
    manual_additional_fields: list[dict[str, str]] | None = None,
) -> dict[str, Any]:
    """Generate the JSON config structure for monthly recap reports.

    Args:
        app_name (str): The application name.
        report_name (str): The report name (e.g., "client_service_monthly_recap").
        soid (str): The service offering ID.
        manual_email_base (str | None): The manually entered email base, if provided.
        email_subject_from_gui (str | None): The email subject entered in the GUI, if any.
        delivery_method_for_monthly (str | None): The delivery method (e.g., "email", "both").
        service_offering_type_from_gui (str | None): The service offering type selected in the GUI.
        custom_integration_module_dict (dict[str, str] | None): The custom integration module dictionary.
        ftp_filepath (str | None): The FTP filepath if FTP delivery is selected.
        manual_additional_fields (list[dict[str, str]] | None): Manual additional fields if provided.

    Returns:
        dict[str, Any]: The generated JSON configuration for a monthly recap report.
    """
    final_email_subject: str
    email_html_str: str
    email_text_str: str
    delivery_configs: dict[str, Any] = {}

    if email_subject_from_gui:  # if user provided a subject
        final_email_subject = email_subject_from_gui
    else:  # Auto-generate subject if GUI input is empty
        subject_service_name_parts = [word.capitalize() for word in soid.split("_")]
        common_suffixes_to_remove = ["Service", "Forms", "Tax", "Permits", "Court"]
        filtered_subject_parts = []
        for part in subject_service_name_parts:
            cleaned_part = part
            for suffix in common_suffixes_to_remove:
                if cleaned_part.endswith(suffix) and len(cleaned_part) > len(suffix):
                    cleaned_part = cleaned_part[: -len(suffix)]
            if cleaned_part:
                filtered_subject_parts.append(cleaned_part)

        if not filtered_subject_parts:
            base_from_report = report_name.replace("_monthly_recap", "")
            if base_from_report.startswith(f"{app_name.lower()}_"):
                base_from_report = base_from_report[len(app_name) + 1 :]
            subject_service_name = " ".join(word.capitalize() for word in base_from_report.split("_"))
        else:
            subject_service_name = " ".join(filtered_subject_parts)
        final_email_subject = f"{app_name} {subject_service_name} Monthly Recap DATE"

    email_html_str = f"<p>{final_email_subject} is attached.</p>"
    email_text_str = f"{final_email_subject} is attached."

    # --- Determine Email Base ---
    final_email_base = generate_email_base(report_name, app_name, manual_email_base)

    # --- Tags generation ---
    tags_list = generate_report_config_tags(app_name, soid, "monthly")

    # Build delivery_configs only if email is part of the delivery method
    if delivery_method_for_monthly in {"email", "both"}:
        delivery_configs["email"] = {
            "html": email_html_str,
            "subject": final_email_subject,
            "tags": tags_list,
            "text": email_text_str,
        }

    if delivery_method_for_monthly in {"ftp", "both"}:
        ftp_config: dict[str, str] = {}
        if ftp_filepath:
            ftp_config["filepath"] = ftp_filepath
        delivery_configs["ftp"] = ftp_config

    # --- Add Kafka delivery config (always included) ---
    delivery_configs["kafka"] = {"prosight": True}

    # output_filename = generate_output_filename(report_name, app_name)

    # Start with empty additional fields for monthly reports
    additional_fields = []
    if manual_additional_fields:
        additional_fields.extend(manual_additional_fields)

    monthly_json_structure: dict[str, Any] = {
        "additional_fields": additional_fields,
        "app_names": [app_name],
        "base_name": "financial_remittance",
        "custom_integration_module": custom_integration_module_dict,
        "delivery_configs": delivery_configs,
        "drop_rows_with_empty_columns": False,
        "email_base": final_email_base,
        # "output_file_name": output_filename,
        "report_name": report_name,
        "service_names": [service_offering_type_from_gui]
        if service_offering_type_from_gui
        else ["Service Offering Type Not Provided"],
        "service_offering_ids": [soid],
    }
    return monthly_json_structure
