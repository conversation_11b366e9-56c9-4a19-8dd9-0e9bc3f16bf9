import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    width: 350
    height: 420
    color: "#ffffff"
    border.color: "#e0e0e0"
    border.width: 1
    radius: 8

    property var selectedDates: []
    property date lastClickedDate: new Date()
    property date currentMonth: new Date()

    signal datesChanged()
    signal clearRequested()

    function addDate(date) {
        var dateString = Qt.formatDate(date, "yyyy-MM-dd")
        if (selectedDates.indexOf(dateString) === -1) {
            selectedDates.push(dateString)
            selectedDates = selectedDates.slice() // Trigger property change
            datesChanged()
        }
    }

    function removeDate(date) {
        var dateString = Qt.formatDate(date, "yyyy-MM-dd")
        var index = selectedDates.indexOf(dateString)
        if (index > -1) {
            selectedDates.splice(index, 1)
            selectedDates = selectedDates.slice() // Trigger property change
            datesChanged()
        }
    }

    function isDateSelected(date) {
        var dateString = Qt.formatDate(date, "yyyy-MM-dd")
        return selectedDates.indexOf(dateString) > -1
    }

    function clearAllDates() {
        selectedDates = []
        datesChanged()
        clearRequested()
    }

    function selectDateRange(startDate, endDate) {
        var start = new Date(startDate)
        var end = new Date(endDate)

        if (start > end) {
            var temp = start
            start = end
            end = temp
        }

        var current = new Date(start)
        while (current <= end) {
            addDate(current)
            current.setDate(current.getDate() + 1)
        }
    }

    function isToday(date) {
        var today = new Date()
        return date.getDate() === today.getDate() &&
               date.getMonth() === today.getMonth() &&
               date.getFullYear() === today.getFullYear()
    }

    function isSameMonth(date, month) {
        return date.getMonth() === month.getMonth() &&
               date.getFullYear() === month.getFullYear()
    }

    function getDaysInMonth(year, month) {
        return new Date(year, month + 1, 0).getDate()
    }

    function getFirstDayOfWeek(year, month) {
        return new Date(year, month, 1).getDay()
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10

        // Header with month navigation
        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 40

            Button {
                id: prevButton
                text: "◀"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 30

                background: Rectangle {
                    color: parent.pressed ? "#d6dbdf" : parent.hovered ? "#eaeded" : "#f8f9fa"
                    radius: 4
                    border.color: "#dee2e6"
                    border.width: 1
                }

                onClicked: {
                    var newDate = new Date(root.currentMonth)
                    newDate.setMonth(newDate.getMonth() - 1)
                    root.currentMonth = newDate
                    calendarGrid.updateCalendar()
                }
            }

            Text {
                id: monthYearLabel
                Layout.fillWidth: true
                text: Qt.formatDate(root.currentMonth, "MMMM yyyy")
                font.pixelSize: 16
                font.weight: Font.Bold
                horizontalAlignment: Text.AlignHCenter
                color: "#2c3e50"
            }

            Button {
                id: nextButton
                text: "▶"
                Layout.preferredWidth: 30
                Layout.preferredHeight: 30

                background: Rectangle {
                    color: parent.pressed ? "#d6dbdf" : parent.hovered ? "#eaeded" : "#f8f9fa"
                    radius: 4
                    border.color: "#dee2e6"
                    border.width: 1
                }

                onClicked: {
                    var newDate = new Date(root.currentMonth)
                    newDate.setMonth(newDate.getMonth() + 1)
                    root.currentMonth = newDate
                    calendarGrid.updateCalendar()
                }
            }
        }

        // Day of week headers
        GridLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 30
            columns: 7

            Repeater {
                model: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    color: "#f8f9fa"
                    border.color: "#dee2e6"
                    border.width: 1

                    Text {
                        anchors.centerIn: parent
                        text: modelData
                        font.pixelSize: 12
                        font.weight: Font.Bold
                        color: "#6c757d"
                    }
                }
            }
        }

        // Calendar grid
        GridLayout {
            id: calendarGrid
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 240
            columns: 7
            rowSpacing: 1
            columnSpacing: 1

            property int year: root.currentMonth.getFullYear()
            property int month: root.currentMonth.getMonth()
            property int daysInMonth: root.getDaysInMonth(year, month)
            property int firstDayOfWeek: root.getFirstDayOfWeek(year, month)
            property int totalCells: 42 // 6 weeks * 7 days

            function updateCalendar() {
                year = root.currentMonth.getFullYear()
                month = root.currentMonth.getMonth()
                daysInMonth = root.getDaysInMonth(year, month)
                firstDayOfWeek = root.getFirstDayOfWeek(year, month)
            }

            Repeater {
                id: dayRepeater
                model: calendarGrid.totalCells

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 35

                    property int dayNumber: {
                        if (index < calendarGrid.firstDayOfWeek) {
                            // Previous month days
                            var prevMonth = calendarGrid.month - 1
                            var prevYear = calendarGrid.year
                            if (prevMonth < 0) {
                                prevMonth = 11
                                prevYear--
                            }
                            var daysInPrevMonth = root.getDaysInMonth(prevYear, prevMonth)
                            return daysInPrevMonth - (calendarGrid.firstDayOfWeek - index - 1)
                        } else if (index < calendarGrid.firstDayOfWeek + calendarGrid.daysInMonth) {
                            // Current month days
                            return index - calendarGrid.firstDayOfWeek + 1
                        } else {
                            // Next month days
                            return index - calendarGrid.firstDayOfWeek - calendarGrid.daysInMonth + 1
                        }
                    }

                    property bool isCurrentMonth: index >= calendarGrid.firstDayOfWeek &&
                                                index < calendarGrid.firstDayOfWeek + calendarGrid.daysInMonth

                    property date cellDate: {
                        if (index < calendarGrid.firstDayOfWeek) {
                            // Previous month
                            var prevMonth = calendarGrid.month - 1
                            var prevYear = calendarGrid.year
                            if (prevMonth < 0) {
                                prevMonth = 11
                                prevYear--
                            }
                            return new Date(prevYear, prevMonth, dayNumber)
                        } else if (index < calendarGrid.firstDayOfWeek + calendarGrid.daysInMonth) {
                            // Current month
                            return new Date(calendarGrid.year, calendarGrid.month, dayNumber)
                        } else {
                            // Next month
                            var nextMonth = calendarGrid.month + 1
                            var nextYear = calendarGrid.year
                            if (nextMonth > 11) {
                                nextMonth = 0
                                nextYear++
                            }
                            return new Date(nextYear, nextMonth, dayNumber)
                        }
                    }

                    color: {
                        if (!isCurrentMonth) return "#f8f9fa"
                        if (root.isDateSelected(cellDate)) return "#007bff"
                        if (dayMouseArea.containsMouse) return "#e3f2fd"
                        if (root.isToday(cellDate)) return "#fff3cd"
                        return "#ffffff"
                    }
                    border.color: "#dee2e6"
                    border.width: 1

                    Behavior on color {
                        ColorAnimation { duration: 150 }
                    }

                    Text {
                        anchors.centerIn: parent
                        text: dayNumber
                        font.pixelSize: 14
                        font.weight: root.isDateSelected(cellDate) ? Font.Bold : Font.Normal
                        color: {
                            if (!isCurrentMonth) return "#adb5bd"
                            if (root.isDateSelected(cellDate)) return "#ffffff"
                            if (root.isToday(cellDate)) return "#856404"
                            return "#495057"
                        }
                    }

                    MouseArea {
                        id: dayMouseArea
                        anchors.fill: parent
                        hoverEnabled: true

                        onClicked: function(mouse) {
                            if (!parent.isCurrentMonth) return

                            var clickedDate = parent.cellDate
                            var isCtrlPressed = mouse.modifiers & Qt.ControlModifier
                            var isShiftPressed = mouse.modifiers & Qt.ShiftModifier

                            if (isShiftPressed && root.selectedDates.length > 0) {
                                // Shift+click: select range from last clicked to current
                                root.selectDateRange(root.lastClickedDate, clickedDate)
                            } else if (isCtrlPressed) {
                                // Ctrl+click: toggle individual date
                                if (root.isDateSelected(clickedDate)) {
                                    root.removeDate(clickedDate)
                                } else {
                                    root.addDate(clickedDate)
                                }
                            } else {
                                // Normal click: select only this date
                                root.selectedDates = []
                                root.addDate(clickedDate)
                            }

                            root.lastClickedDate = clickedDate
                        }
                    }
                }
            }
        }

        // Bottom controls
        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 30

            Text {
                Layout.fillWidth: true
                text: root.selectedDates.length === 0 ? "No dates selected" :
                      root.selectedDates.length === 1 ? "1 date selected" :
                      root.selectedDates.length + " dates selected"
                font.pixelSize: 12
                color: "#6c757d"
            }

            Button {
                text: "Clear All"
                Layout.preferredWidth: 80
                Layout.preferredHeight: 26
                enabled: root.selectedDates.length > 0

                background: Rectangle {
                    color: parent.enabled ? (parent.pressed ? "#c82333" : parent.hovered ? "#e53e3e" : "#dc3545") : "#f8f9fa"
                    radius: 4
                    border.color: parent.enabled ? "#dc3545" : "#dee2e6"
                    border.width: 1
                }

                contentItem: Text {
                    text: parent.text
                    font.pixelSize: 11
                    color: parent.enabled ? "#ffffff" : "#6c757d"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                onClicked: root.clearAllDates()
            }

            Button {
                text: "Today"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 26

                background: Rectangle {
                    color: parent.pressed ? "#5a6268" : parent.hovered ? "#6c757d" : "#6c757d"
                    radius: 4
                    border.color: "#6c757d"
                    border.width: 1
                }

                contentItem: Text {
                    text: parent.text
                    font.pixelSize: 11
                    color: "#ffffff"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                onClicked: {
                    var today = new Date()
                    root.currentMonth = today
                    calendarGrid.updateCalendar()
                    root.selectedDates = []
                    root.addDate(today)
                    root.lastClickedDate = today
                }
            }
        }
    }
}
