"""Miscellaneous helpers for GUI code that are *not* widgets themselves."""

from PySide6.QtCore import QObject


class SignalBlocker:
    """Context-manager that blocks signals for a given Qt widget while you mutate it.

    Usage::

        with SignalBlocker(self.line_edit):
            self.line_edit.setText("foo")
    """

    def __init__(self, widget: QObject):
        self._w = widget

    def __enter__(self) -> None:
        self._w.blockSignals(True)

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self._w.blockSignals(False)
