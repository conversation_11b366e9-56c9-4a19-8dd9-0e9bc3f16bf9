"""GUI widget utilities for the report configuration generator."""

from typing import Callable, Optional, Sequence

from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFontMetrics, QIcon
from PySide6.QtWidgets import QCheckBox, QComboBox, QLabel, QLineEdit, QPushButton, QRadioButton, QWidget


class RunReportButtonManager:
    """Manages the visual state and spinner animation for the run report button."""

    def __init__(self, button: QPushButton, spinner_timer: QTimer, spinner_chars: list[str]):
        """Initialize the button manager.

        Args:
            button (QPushButton): The run report button to manage.
            spinner_timer (QTimer): Timer for spinner animation.
            spinner_chars (list[str]): List of spinner characters for animation.
        """
        self.button = button
        self.spinner_timer = spinner_timer
        self.spinner_chars = spinner_chars
        self.spinner_index = 0

    def set_state(self, state: str) -> None:
        """Set the visual state of the run report button.

        Args:
            state (str): The state to set - 'normal', 'running', 'success', or 'error'
        """
        if state == "running":
            # Yellow state with spinning icon - running
            self.button.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
                QPushButton:pressed {
                    background-color: #d35400;
                }
            """)
            # Start spinner animation
            self.spinner_index = 0
            self.spinner_timer.start(150)  # Update every 150ms
            self.button.setText(f"{self.spinner_chars[0]} Running...")
        elif state == "success":
            # Green state - success
            self.spinner_timer.stop()
            self.button.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
            """)
            self.button.setText("Run Report")
        elif state == "error":
            # Red state - error
            self.spinner_timer.stop()
            self.button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            self.button.setText("Run Report")
        else:
            # Normal state - default gray
            self.spinner_timer.stop()
            self.button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
                QPushButton:pressed {
                    background-color: #6c7b7d;
                }
            """)
            self.button.setText("Run Report")

    def update_spinner(self) -> None:
        """Update the spinner animation for the running state."""
        spinner_char = self.spinner_chars[self.spinner_index]
        self.button.setText(f"{spinner_char} Running...")
        self.spinner_index = (self.spinner_index + 1) % len(self.spinner_chars)


def auto_resize_text_field(text_field: QLineEdit, text: str, min_width: int = 200, max_width: int = 600) -> None:
    """Auto-resize a text field to fit the content.

    Args:
        text_field (QLineEdit): The text field to resize.
        text (str): The current text in the field.
        min_width (int): Minimum width for the field.
        max_width (int): Maximum width for the field.
    """
    if not text.strip():
        # Reset to minimum width when empty
        text_field.setMinimumWidth(min_width)
        return

    font_metrics = QFontMetrics(text_field.font())
    text_width = font_metrics.horizontalAdvance(text)
    padding = 20
    required_width = text_width + padding
    actual_width = max(min_width, min(required_width, max_width))
    text_field.setMinimumWidth(actual_width)


class WidgetFactory:
    """Factory for creating commonly used widgets with consistent styling."""

    @staticmethod
    def create_line_edit(placeholder: str = "", tooltip: str = "") -> QLineEdit:
        """Create a standardized QLineEdit.

        Args:
            placeholder (str): Placeholder text for the line edit.
            tooltip (str): Tooltip text for the line edit.

        Returns:
            QLineEdit: The configured line edit widget.
        """
        edit = QLineEdit()
        if placeholder:
            edit.setPlaceholderText(placeholder)
        if tooltip:
            edit.setToolTip(tooltip)
        return edit

    @staticmethod
    def create_combo_box(items: list[str], default: str = "", tooltip: str = "") -> QComboBox:
        """Create a standardized QComboBox.

        Args:
            items (list[str]): List of items to add to the combo box.
            default (str): Default selected item.
            tooltip (str): Tooltip text for the combo box.

        Returns:
            QComboBox: The configured combo box widget.
        """
        combo = QComboBox()
        combo.addItems(items)
        if default:
            combo.setCurrentText(default)
        if tooltip:
            combo.setToolTip(tooltip)
        return combo

    @staticmethod
    def create_checkbox(text: str, tooltip: str = "", checked: bool = False) -> QCheckBox:
        """Create a standardized QCheckBox with consistent styling.

        Args:
            text (str): Text label for the checkbox.
            tooltip (str): Tooltip text for the checkbox.
            checked (bool): Initial checked state.

        Returns:
            QCheckBox: The configured checkbox widget.
        """
        checkbox = QCheckBox(text)
        checkbox.setChecked(checked)
        if tooltip:
            checkbox.setToolTip(tooltip)

        # Apply consistent styling with proper spacing
        checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                spacing: 8px;
                margin-right: 15px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        return checkbox

    @staticmethod
    def create_styled_button(text: str, style: str = "primary", tooltip: str = "") -> QPushButton:
        """Create a standardized QPushButton with consistent styling.

        Args:
            text (str): Button text.
            style (str): Button style - 'primary', 'secondary', or 'normal'.
            tooltip (str): Tooltip text for the button.

        Returns:
            QPushButton: The configured button widget.
        """
        button = QPushButton(text)
        if tooltip:
            button.setToolTip(tooltip)

        if style == "primary":
            button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
        elif style == "secondary":
            button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
                QPushButton:pressed {
                    background-color: #6c7b7d;
                }
            """)
        return button

    @staticmethod
    def create_icon_button(icon_name: str = "", tooltip: str = "") -> QPushButton:
        """Create a small, flat icon button.

        Args:
            icon_name (str): The name of the icon. If the corresponding theme icon is not found, the
                icon will fall back to a unicode character (currently only supports "search").
            tooltip (str): Optional tooltip text.

        Returns:
            QPushButton: The configured icon button.
        """
        button = QPushButton()
        button.setFlat(True)
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        button.setFixedSize(24, 24)

        # Attempt to load the icon from the system theme first
        icon = QIcon.fromTheme(icon_name) if icon_name else QIcon()
        if not icon.isNull():
            button.setIcon(icon)
        else:
            # Fallback: use a unicode character for common icons
            if icon_name == "search":
                button.setText("🔍")
            else:
                button.setText(icon_name)
        if tooltip:
            button.setToolTip(tooltip)

        # Minimal styling to match other secondary buttons
        button.setStyleSheet(
            "QPushButton { border: none; background: transparent; }"
            "QPushButton:hover { background-color: #e9ecef; border-radius: 4px; }"
        )
        return button

    @staticmethod
    def create_radio_button(text: str, tooltip: str = "", checked: bool = False) -> QRadioButton:
        """Create a standardized QRadioButton with consistent styling.

        Args:
            text (str): Text label for the radio button.
            tooltip (str): Tooltip text for the radio button.
            checked (bool): Initial checked state.

        Returns:
            QRadioButton: The configured radio button widget.
        """
        radio = QRadioButton(text)
        radio.setChecked(checked)
        if tooltip:
            radio.setToolTip(tooltip)

        # Apply consistent styling with proper spacing
        radio.setStyleSheet("""
            QRadioButton {
                font-size: 13px;
                spacing: 8px;
                margin-right: 15px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        return radio


class VisibilityManager:
    """Manages widget visibility toggling patterns."""

    @staticmethod
    def create_toggle_handler(widgets: Sequence[QWidget], labels: Optional[Sequence[Optional[QLabel]]] = None):
        """Create a generic visibility toggle handler.

        Args:
            widgets (Sequence[QWidget]): Sequence of widgets to toggle visibility.
            labels (Optional[Sequence[Optional[QLabel]]]): Sequence of labels to toggle visibility.

        Returns:
            Callable: Handler function that can be connected to toggle signals.
        """

        def handler(checked: bool) -> None:
            for widget in widgets:
                widget.setVisible(checked)
            if labels:
                for label in labels:
                    if label:  # Check if label exists
                        label.setVisible(checked)

        return handler

    @staticmethod
    def create_conditional_visibility_handler(
        condition_widgets: Sequence[QWidget],
        target_widgets: Sequence[QWidget],
        labels: Optional[Sequence[Optional[QLabel]]] = None,
        condition_check: Optional[Callable[[Sequence[QWidget]], bool]] = None,
    ):
        """Create a handler that shows/hides widgets based on multiple conditions.

        Args:
            condition_widgets (Sequence[QWidget]): Widgets whose state determines visibility.
            target_widgets (Sequence[QWidget]): Widgets to show/hide.
            labels (Optional[Sequence[Optional[QLabel]]]): Labels to show/hide.
            condition_check (Optional[Callable[[Sequence[QWidget]], bool]]): Custom condition function.

        Returns:
            Callable: Handler function for complex visibility logic.
        """

        def handler() -> None:
            if condition_check:
                show = condition_check(condition_widgets)
            else:
                # Default: show if any checkbox is checked
                show = any(widget.isChecked() for widget in condition_widgets if hasattr(widget, "isChecked"))

            for widget in target_widgets:
                widget.setVisible(show)
            if labels:
                for label in labels:
                    if label:
                        label.setVisible(show)

        return handler
