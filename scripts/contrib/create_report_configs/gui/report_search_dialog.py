"""Dialog window that provides a searchable list of existing report configuration names.

The dialog scans the ``payit_remittance_report/configs`` directory each time it is opened and
lists all ``*.json`` files without the extension. Users can filter the list in real time and
select a report name. Once a selection is made the dialog is accepted so that the caller can
retrieve the chosen value via :py:meth:`get_selected_report_names`.

The implementation is intentionally single-select for now, but the internal list widget is
configured with ``ExtendedSelection`` so that switching to multi-select is trivial in the
future.
"""

from __future__ import annotations

from pathlib import Path
from typing import Iterable

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QAbstractItemView,
    QDialog,
    QHBoxLayout,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QPushButton,
    QVBoxLayout,
    QWidget,
)

from scripts.contrib.create_report_configs.gui.widgets import WidgetFactory
from scripts.contrib.create_report_configs.models.constants import JSON_CONFIG_DIR


class ReportSearchDialog(QDialog):
    """Modal dialog for searching and selecting report names."""

    def __init__(self, parent: QWidget | None = None):
        """Construct the dialog and populate the UI.

        Args:
            parent (QWidget | None): Parent widget.
        """
        super().__init__(parent)
        self.setWindowModality(Qt.WindowModality.WindowModal)
        self.setWindowTitle("Search Report Configs")
        self.setMinimumWidth(450)

        self._selected_reports: list[str] = []

        # --- Widgets ---------------------------------------------------------------------
        self._search_edit = QLineEdit()
        self._search_edit.setPlaceholderText("Type to filter reports…")

        self._list_widget = QListWidget()
        # Allow future multi-select without code changes
        self._list_widget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)

        ok_button = WidgetFactory.create_styled_button("OK", style="primary")
        cancel_button = QPushButton("Cancel")

        # --- Layout ----------------------------------------------------------------------
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self._search_edit)
        main_layout.addWidget(self._list_widget, 1)

        button_bar = QHBoxLayout()
        button_bar.addStretch(1)
        button_bar.addWidget(ok_button)
        button_bar.addWidget(cancel_button)
        main_layout.addLayout(button_bar)

        # --- Signals ---------------------------------------------------------------------
        self._search_edit.textChanged.connect(self._apply_filter)
        self._list_widget.itemDoubleClicked.connect(self._on_item_activated)
        ok_button.clicked.connect(self._on_ok_clicked)
        cancel_button.clicked.connect(self.reject)

        # Initial population
        self._all_report_names: list[str] = []
        self._refresh_report_names()
        self._apply_filter("")

    # --------------------------------------------------------------------- Public helpers
    def get_selected_report_names(self) -> list[str]:
        """Return all selected report names."""
        return list(self._selected_reports)

    # -------------------------------------------------------------------- Private helpers
    def _refresh_report_names(self) -> None:
        """Reload report names from the configuration directory."""
        self._all_report_names = sorted(_discover_report_names(JSON_CONFIG_DIR))

    def _apply_filter(self, filter_text: str) -> None:
        """Filter list widget items based on *filter_text*."""
        filter_lower = filter_text.lower()
        self._list_widget.clear()
        for name in self._all_report_names:
            if filter_lower in name.lower():
                self._list_widget.addItem(name)

    def _on_item_activated(self, item: QListWidgetItem) -> None:
        """Handle item activation (double-click)."""
        self._selected_reports = [item.text()]
        self.accept()

    def _on_ok_clicked(self) -> None:
        """Collect current selections and close the dialog."""
        self._selected_reports = [item.text() for item in self._list_widget.selectedItems()]
        self.accept()


# --------------------------------------------------------------------------- Utilities


def _discover_report_names(config_dir: Path) -> Iterable[str]:
    """Yield report names (stem of JSON files) contained in *config_dir* recursively."""
    for json_file in config_dir.glob("*.json"):
        if json_file.is_file():
            yield json_file.stem
    # Recurse into sub-directories if any (future proof)
    for sub_dir in [p for p in config_dir.iterdir() if p.is_dir()]:
        yield from _discover_report_names(sub_dir)
