"""Modern QML-based multi-date calendar widget for report runner."""

from pathlib import Path

from PySide6.QtCore import QDate, QDateTime, QTime, QUrl, Signal
from PySide6.QtQuick import QQuickItem
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtWidgets import Q<PERSON><PERSON>ox<PERSON>ayout, QLabel, QPushButton, QVBoxLayout, QWidget


class MultiDateCalendar(QWidget):
    """A compact calendar button that opens a popup calendar for multi-date selection."""

    dates_changed = Signal()  # Emitted when the selected dates change

    def __init__(self, parent: QWidget | None = None):
        """Initialize the multi-date calendar widget.

        Args:
            parent (QWidget | None): Parent widget.
        """
        super().__init__(parent)
        self._is_calendar_visible = False
        self._setup_ui()

    def _setup_ui(self) -> None:
        """Set up the user interface with calendar button and popup."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # Create horizontal layout for button and selected dates display
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)

        # Calendar button with icon
        self.calendar_button = QPushButton("📅 Select Dates")
        self.calendar_button.setFixedHeight(32)
        self.calendar_button.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 13px;
                color: #495057;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
                border-color: #6c757d;
            }
        """)
        self.calendar_button.clicked.connect(self._toggle_calendar)

        # Selected dates display label
        self.selected_dates_label = QLabel("No dates selected")
        self.selected_dates_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 12px;
                background-color: transparent;
            }
        """)

        button_layout.addWidget(self.calendar_button)
        button_layout.addWidget(self.selected_dates_label, 1)

        layout.addLayout(button_layout)

        # Create QML calendar widget (initially hidden)
        self.qml_widget = QQuickWidget()
        self.qml_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)

        # Load the QML file
        qml_file_path = Path(__file__).parent / "MultiDateCalendar.qml"
        qml_url = QUrl.fromLocalFile(str(qml_file_path))

        # Ensure QML file exists
        if not qml_file_path.exists():
            raise FileNotFoundError(f"QML file not found: {qml_file_path}")

        self.qml_widget.setSource(qml_url)

        # Set fixed size for the calendar popup
        self.qml_widget.setFixedSize(350, 420)

        # Initially hide the calendar
        self.qml_widget.setVisible(False)

        # Get root object to connect signals
        self.root_object: QQuickItem | None = self.qml_widget.rootObject()
        if self.root_object:
            # Type ignore needed because mypy doesn't know about custom QML signals
            self.root_object.datesChanged.connect(self._on_qml_dates_changed)  # type: ignore[attr-defined]
            self.root_object.clearRequested.connect(self._on_qml_clear_requested)  # type: ignore[attr-defined]
        else:
            print("Warning: Could not get QML root object")

        layout.addWidget(self.qml_widget)

    def _toggle_calendar(self) -> None:
        """Toggle the visibility of the calendar popup."""
        self._is_calendar_visible = not self._is_calendar_visible
        self.qml_widget.setVisible(self._is_calendar_visible)

        # Update button text based on state
        if self._is_calendar_visible:
            self.calendar_button.setText("📅 Hide Calendar")
        else:
            self.calendar_button.setText("📅 Select Dates")

    def _update_selected_dates_display(self) -> None:
        """Update the display showing selected dates count."""
        if not self.root_object:
            self.selected_dates_label.setText("No dates selected")
            return

        selected_dates_qjs = self.root_object.property("selectedDates")
        if not selected_dates_qjs:
            selected_dates_strings: list[str] = []
        else:
            # Convert QJSValue to Python list
            if hasattr(selected_dates_qjs, "toVariant"):
                selected_dates_strings = selected_dates_qjs.toVariant() or []
            else:
                selected_dates_strings = []

        count = len(selected_dates_strings)

        if count == 0:
            self.selected_dates_label.setText("No dates selected")
        elif count == 1:
            # Show the single date
            self.selected_dates_label.setText(f"Selected: {selected_dates_strings[0]}")
        else:
            # Show count and date range
            sorted_dates = sorted(selected_dates_strings)
            first_date = sorted_dates[0]
            last_date = sorted_dates[-1]
            if count == 2:
                self.selected_dates_label.setText(f"Selected: {first_date}, {last_date}")
            else:
                self.selected_dates_label.setText(f"Selected: {count} dates ({first_date} to {last_date})")

    def _on_qml_dates_changed(self) -> None:
        """Handle dates changed signal from QML."""
        self._update_selected_dates_display()
        self.dates_changed.emit()

    def _on_qml_clear_requested(self) -> None:
        """Handle clear requested signal from QML."""
        self._update_selected_dates_display()

    def get_selected_dates(self) -> list[QDate]:
        """Get the currently selected dates.

        Returns:
            list[QDate]: List of selected dates, sorted chronologically.
        """
        if not self.root_object:
            return []

        selected_dates_qjs = self.root_object.property("selectedDates")
        if not selected_dates_qjs:
            selected_dates_strings: list[str] = []
        else:
            # Convert QJSValue to Python list
            if hasattr(selected_dates_qjs, "toVariant"):
                selected_dates_strings = selected_dates_qjs.toVariant() or []
            else:
                selected_dates_strings = []

        dates = []
        for date_str in selected_dates_strings:
            date = QDate.fromString(date_str, "yyyy-MM-dd")
            if date.isValid():
                dates.append(date)

        return sorted(dates)

    def set_selected_dates(self, dates: list[QDate]) -> None:
        """Set the selected dates.

        Args:
            dates (list[QDate]): List of dates to select.
        """
        if not self.root_object:
            return

        # Convert QDate objects to strings
        date_strings = [date.toString("yyyy-MM-dd") for date in dates]

        # Update QML property
        self.root_object.setProperty("selectedDates", date_strings)

        # Update last clicked date if we have dates
        if dates:
            last_date = max(dates)
            # Convert to JavaScript Date for QML - need to create proper JS Date
            qt_datetime = QDateTime(last_date, QTime(0, 0))
            js_date = qt_datetime.toPython()
            self.root_object.setProperty("lastClickedDate", js_date)

        # Update display
        self._update_selected_dates_display()

    def clear_selections(self) -> None:
        """Clear all selected dates."""
        if self.root_object:
            # Call QML function to clear dates
            self.root_object.clearAllDates()  # type: ignore[attr-defined]
            self._update_selected_dates_display()

    def get_selected_dates_as_strings(self) -> list[str]:
        """Get selected dates as formatted strings.

        Returns:
            list[str]: List of dates in YYYY-MM-DD format.
        """
        if not self.root_object:
            return []

        selected_dates_qjs = self.root_object.property("selectedDates")
        if not selected_dates_qjs:
            selected_dates_strings: list[str] = []
        else:
            # Convert QJSValue to Python list
            if hasattr(selected_dates_qjs, "toVariant"):
                selected_dates_strings = selected_dates_qjs.toVariant() or []
            else:
                selected_dates_strings = []

        return sorted(selected_dates_strings)
