"""Main GUI window for the report configuration generator."""

from typing import Any, cast

from payit_data_common.api.models.scheduled_job import Ftp
from PySide6.QtCore import QProcess, Qt, QTime, QTimer
from PySide6.QtWidgets import (
    QApplication,
    QButtonGroup,
    QComboBox,
    QDialog,
    QFormLayout,
    QFrame,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QMenu,
    QMessageBox,
    QPushButton,
    QRadioButton,
    QScrollArea,
    QSizePolicy,
    QSpinBox,
    QSplitter,
    QTextEdit,
    QTimeEdit,
    QVBoxLayout,
    QWidget,
)

from scripts.contrib.create_report_configs.core.config_generator import ConfigGenerator
from scripts.contrib.create_report_configs.core.daily_snapshot_loader import (
    ConfigNotFoundError,
    InvalidConfigError,
    load_daily_snapshot,
)
from scripts.contrib.create_report_configs.core.report_runner import ReportRunner
from scripts.contrib.create_report_configs.core.validators import validate_all_inputs, validate_run_report_inputs
from scripts.contrib.create_report_configs.gui.helpers import SignalBlocker
from scripts.contrib.create_report_configs.gui.multi_date_calendar import MultiDateCalendar
from scripts.contrib.create_report_configs.gui.report_search_dialog import ReportSearchDialog
from scripts.contrib.create_report_configs.gui.validators import GuiValidator
from scripts.contrib.create_report_configs.gui.widgets import (
    RunReportButtonManager,
    VisibilityManager,
    WidgetFactory,
    auto_resize_text_field,
)
from scripts.contrib.create_report_configs.models.constants import (
    DAILY_REPORT_SUFFIX,
    MONTHLY_REPORT_SUFFIX,
    PROJECT_ROOT,
    SERVICE_OFFERING_OPTIONS,
    TIMEZONE_OPTIONS,
    TOOLTIPS,
)
from scripts.contrib.create_report_configs.models.daily_report_snapshot import DailyReportSnapshot
from scripts.contrib.create_report_configs.models.report_config import FormData, RunReportData
from scripts.contrib.create_report_configs.utils.schedule_slot_analyzer import ScheduleSlotAnalyzer


class ConfigGeneratorWindow(QWidget):
    """Main window for the configuration generator GUI."""

    def __init__(self):
        """Initialize the ConfigGeneratorWindow."""
        super().__init__()
        self.setWindowTitle("Remittance Report Config Generator")
        self.setMinimumSize(550, 400)
        self.resize(750, 800)

        # Initialize session state for run report configuration
        # (TODO: probably a better way to do this. Will look into it later)
        self._spinner_timer = QTimer()
        self._spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]

        # Initialize components
        self.config_generator = ConfigGenerator(log_callback=self._log_message)
        self.report_runner = ReportRunner(log_callback=self._log_message)
        self.validator = GuiValidator(self)

        # Track the output_file_name for S3 lookup
        self._daily_output_file_name = None

        # Initialize widgets
        self._init_widgets()
        self._setup_layout()
        self._connect_signals()

        self.button_manager = RunReportButtonManager(self.run_report_button, self._spinner_timer, self._spinner_chars)
        self._spinner_timer.timeout.connect(self.button_manager.update_spinner)

        # Set initial focus and visibility
        self.app_name_edit.setFocus()
        self._on_advanced_remittance_toggled(False)
        self._on_frequency_selection_changed()

        # Set initial visibility for service offering dependent fields
        self._on_service_offering_changed(self.service_offering_combo.currentText())

        # Log initial message
        self._log_message("Logger Window initialized. Ready.", "black")

        # Connect handler for manual daily report name entry
        self.daily_report_name_edit.editingFinished.connect(self._on_daily_report_name_editing_finished)

        # Dialog for popped-out selected reports list (None when not open)
        self._selected_reports_dialog: QDialog | None = None

        # Add custom context menu for clearing logs
        self.log_area.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.log_area.customContextMenuRequested.connect(self._on_log_area_context_menu)

    def _init_widgets(self) -> None:
        """Initialize all widgets."""
        # Basic input widgets
        self.app_name_edit = WidgetFactory.create_line_edit()
        self.report_name_edit = WidgetFactory.create_line_edit(
            placeholder="client_state_service_financial_remittance. Ex: buffalo_ny_zoning_forms_financial_remittance"
        )

        self.timezone_combo = WidgetFactory.create_combo_box(TIMEZONE_OPTIONS)

        # Run Report checkbox and fields
        self.run_report_check = WidgetFactory.create_checkbox(
            "Enable Run Report", tooltip=TOOLTIPS["Enable Run Report:"]
        )

        # Environment selection group
        self.environment_group = QWidget()
        self.environment_group.setContentsMargins(20, 0, 0, 0)
        self.environment_group.setToolTip(TOOLTIPS["Environment Selection:"])
        environment_layout = QHBoxLayout()
        environment_layout.setContentsMargins(0, 0, 0, 0)
        self.dev_radio = WidgetFactory.create_radio_button("Dev")
        self.staging_radio = WidgetFactory.create_radio_button("Staging", checked=True)  # Default to staging

        # Create button group for environment selection
        self.environment_button_group = QButtonGroup(self)
        self.environment_button_group.addButton(self.dev_radio)
        self.environment_button_group.addButton(self.staging_radio)

        environment_layout.addWidget(self.dev_radio)
        environment_layout.addWidget(self.staging_radio)
        environment_layout.addStretch()
        self.environment_group.setLayout(environment_layout)
        self.environment_group.setVisible(False)  # Initially hidden

        # Remittance type radio buttons
        self.combined_radio = QRadioButton("Combined")
        self.return_radio = QRadioButton("Success/Return")
        self.remittance_type_group = QButtonGroup(self)
        self.remittance_type_group.addButton(self.combined_radio)
        self.remittance_type_group.addButton(self.return_radio)
        self.combined_radio.setChecked(True)  # Default

        # Advanced Remittance Checkbox
        self.advanced_remittance_check = WidgetFactory.create_checkbox(
            "Advanced Remittance Selections", tooltip=TOOLTIPS["Advanced Remittance Selections:"]
        )

        # Advanced Remittance Dropdowns (initially hidden)
        self._init_advanced_remittance_widgets()

        self.service_offering_combo = WidgetFactory.create_combo_box(SERVICE_OFFERING_OPTIONS)

        # Add-Ons Checkboxes (Conditional)
        self._init_addon_widgets()

        # Additional Fields Section
        self._init_additional_fields_widgets()

        # Email Base Widgets
        self._init_email_base_widgets()

        self.soid_edit = WidgetFactory.create_line_edit()

        # Delivery Config Checkboxes
        self.email_check = WidgetFactory.create_checkbox("Email")
        self.ftp_check = WidgetFactory.create_checkbox("FTP")

        # FTP Filepath Input (for FTP delivery)
        self.ftp_filepath_edit = WidgetFactory.create_line_edit()

        # FTP Credential Dropdown (for Kubernetes Secret)
        ftp_options = [member.value for member in Ftp]
        self.ftp_credential_combo = WidgetFactory.create_combo_box(
            ftp_options, default=Ftp.FINE_REPORTS_PAYIT_FTP_SECRET.value
        )

        # Email Subject Input (for Email delivery)
        self.email_subject_edit = WidgetFactory.create_line_edit(
            placeholder="Appname Service Remittance DATE. Ex: BuffaloNYBillPay Zoning Financial Remittance DATE"
        )

        # Delivery Frequency Radio Buttons
        self.daily_frequency_check = WidgetFactory.create_checkbox("Daily", checked=True)
        self.monthly_frequency_check = WidgetFactory.create_checkbox("Monthly")

        # Schedule and report name widgets
        self._init_schedule_widgets()

        # Logger Window
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        self.log_area.setMinimumHeight(50)
        self.log_area.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Show Available Times button
        self.show_available_times_button = WidgetFactory.create_styled_button(
            "Show Available Times", style="secondary", tooltip=TOOLTIPS["Show Available Times:"]
        )

        # Control buttons and checkboxes
        self.create_button = WidgetFactory.create_styled_button("Create Files", style="primary")

        self.json_only_check = WidgetFactory.create_checkbox("Create JSON Only", tooltip=TOOLTIPS["Create JSON Only:"])

        self.overwrite_check = WidgetFactory.create_checkbox(
            "Overwrite Existing Files", tooltip=TOOLTIPS["Overwrite Existing Files:"]
        )

        # Run Report Date field (initially hidden) - Multi-date calendar
        self.run_report_date_calendar = MultiDateCalendar()
        self.run_report_date_calendar.setToolTip(TOOLTIPS["Run Report Date:"])
        self.run_report_date_calendar.dates_changed.connect(self._on_report_dates_changed)

        # Auto-find date checkbox for run report
        self.auto_find_date_run_report_check = WidgetFactory.create_checkbox(
            "Auto-find date", tooltip=TOOLTIPS["Auto-find date (Run Report):"]
        )

        # Search button for report name selection
        self.run_report_search_button = WidgetFactory.create_icon_button(
            "search",
            tooltip="Search available report configurations",
        )

        # Run Report Name field with auto-populate checkbox (initially hidden)
        self.run_report_name_edit = WidgetFactory.create_line_edit(placeholder="Enter custom report name...")
        self.run_report_name_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        # Connect text changed to auto-resize function
        self.run_report_name_edit.textChanged.connect(self._on_run_report_name_text_changed)

        self.run_report_auto_populate_check = WidgetFactory.create_checkbox(
            "Auto-populate", tooltip=TOOLTIPS["Auto-populate Report Name:"], checked=True
        )

        # Run Report and Cancel All buttons (side-by-side)
        self.run_report_button = WidgetFactory.create_styled_button(
            "Run Report", style="secondary", tooltip=TOOLTIPS["Run Report Button:"]
        )

        self.cancel_all_button = WidgetFactory.create_styled_button("Cancel All", style="secondary")
        self.cancel_all_button.setVisible(False)

        self._run_buttons_container = QWidget()
        _btn_h_layout = QHBoxLayout(self._run_buttons_container)
        _btn_h_layout.setContentsMargins(0, 0, 0, 0)
        _btn_h_layout.setSpacing(6)
        _btn_h_layout.addWidget(self.run_report_button)
        _btn_h_layout.addWidget(self.cancel_all_button)
        _btn_h_layout.addStretch(1)

        # Selected Reports container (hidden until reports are added)
        from PySide6.QtWidgets import QVBoxLayout as _QVBox  # local import to avoid circular

        self.selected_reports_container = QWidget()
        self.selected_reports_layout = _QVBox()
        self.selected_reports_layout.setContentsMargins(0, 0, 0, 0)
        self.selected_reports_layout.setSpacing(1)  # tighter vertical spacing
        self.selected_reports_container.setLayout(self.selected_reports_layout)
        self.selected_reports_container.setVisible(False)

        # Scroll area wrapper so the list remains usable with many selections.
        self.selected_reports_scroll_area = QScrollArea()
        self.selected_reports_scroll_area.setWidgetResizable(True)
        self.selected_reports_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.selected_reports_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.selected_reports_scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        self.selected_reports_scroll_area.setWidget(self.selected_reports_container)
        self.selected_reports_scroll_area.setVisible(False)

        # Internal tracking structures
        self._selected_reports: set[str] = set()
        self._selected_report_rows: dict[str, QWidget] = {}
        self._report_processes: dict[str, QProcess] = {}
        self._report_colors: dict[str, str] = {}

        # Per-report date storage
        self._report_dates: dict[str, str] = {}  # report_name -> date_string (YYYY-MM-DD)
        self._report_date_buttons: dict[str, QPushButton] = {}  # report_name -> calendar button

    def _init_advanced_remittance_widgets(self) -> None:
        """Initialize advanced remittance dropdown widgets."""
        self.advanced_remittance_widgets: dict[str, QComboBox] = {}

        # Define specific option lists based on requirements
        transaction_status_options = ["default", "COMBINED", "RETURN"]
        same_day_status_options = ["default", "COMBINED", "SUCCESS/RETURN"]
        success_status_options = ["default", "COMBINED", "SUCCESS"]

        advanced_keys_map: dict[str, list[str]] = {
            "Cancellation": transaction_status_options,
            "Chargeback": transaction_status_options,
            "Failure": transaction_status_options,
            "Refund": transaction_status_options,
            "Same Day Cancellation": same_day_status_options,
            "Same Day Failure": same_day_status_options,
            "Same Day Refund": same_day_status_options,
            "Success": success_status_options,
        }

        for key, options_list in advanced_keys_map.items():
            combo = QComboBox()
            combo.addItems(options_list)
            combo.setCurrentText("default")
            combo.setVisible(False)
            combo.setMinimumWidth(170)
            self.advanced_remittance_widgets[key] = combo

    def _init_addon_widgets(self) -> None:
        """Initialize add-on widgets."""
        self.ivr_addon_check = WidgetFactory.create_checkbox("IVR")
        self.integrated_pos_addon_check = WidgetFactory.create_checkbox("Integrated POS")

    def _init_additional_fields_widgets(self) -> None:
        """Initialize additional fields widgets."""
        # Checkbox to enable additional fields
        self.additional_fields_check = WidgetFactory.create_checkbox(
            "Add Additional Fields", tooltip=TOOLTIPS["Add Additional Fields:"]
        )

        # Container for the dynamic additional fields
        self.additional_fields_container = QWidget()
        self.additional_fields_layout = QVBoxLayout(self.additional_fields_container)
        self.additional_fields_layout.setContentsMargins(20, 0, 0, 0)  # Indent the container
        self.additional_fields_layout.setSpacing(2)  # Reduce spacing between rows

        # Add initial "+" button
        self.add_field_button = QPushButton("+ Add Field")
        self.add_field_button.setMaximumWidth(120)

        # Initial container layout
        self.additional_fields_layout.addWidget(self.add_field_button)
        self.additional_fields_layout.addStretch()

        # List to track additional field rows
        self.additional_field_rows: list[dict[str, QWidget | QLineEdit | QPushButton]] = []

        # Initially hidden
        self.additional_fields_check.setVisible(False)
        self.additional_fields_container.setVisible(False)

    def _init_email_base_widgets(self) -> None:
        """Initialize email base widgets."""
        self.email_base_auto_check = WidgetFactory.create_checkbox(
            "Auto Generate", tooltip=TOOLTIPS["Email Base Auto Generate:"], checked=True
        )

        self.email_base_manual_edit = WidgetFactory.create_line_edit(
            placeholder="Enter email base. This must match the google group config."
        )
        self.email_base_manual_edit.setVisible(False)

    def _init_schedule_widgets(self) -> None:
        """Initialize schedule-related widgets."""
        # Day of Month SpinBox (for monthly)
        self.monthly_day_of_month_spinbox = QSpinBox()
        self.monthly_day_of_month_spinbox.setRange(1, 28)
        self.monthly_day_of_month_spinbox.setValue(1)

        # Date With Transactions (for monthly)
        self.date_with_transactions_edit = WidgetFactory.create_line_edit(placeholder="YYYY-MM-DD")

        # Auto-find date checkbox for monthly reports
        self.auto_find_date_monthly_check = WidgetFactory.create_checkbox(
            "Auto-find date", tooltip=TOOLTIPS["Auto-find date (Monthly):"]
        )

        # Daily Report Name field with search button
        self.daily_report_name_edit = WidgetFactory.create_line_edit(
            placeholder="Enter name of corresponding daily report for this monthly report",
        )

        # Search button (magnifying glass) for Daily Report Name
        self.daily_report_search_button = WidgetFactory.create_icon_button(
            "search",
            tooltip="Search available daily report configurations",
        )

        # Monthly Report Name (for both daily and monthly)
        self.monthly_report_name_edit = WidgetFactory.create_line_edit(
            placeholder="Auto-generated from Report Name. Ex: caleb_test_monthly_recap"
        )

        self.daily_schedule_time_edit = QTimeEdit()
        self.daily_schedule_time_edit.setDisplayFormat("HH:mm")
        self.daily_schedule_time_edit.setTime(QTime(10, 0))

        self.monthly_schedule_time_edit = QTimeEdit()
        self.monthly_schedule_time_edit.setDisplayFormat("HH:mm")
        self.monthly_schedule_time_edit.setTime(QTime(10, 30))  # Default 30 mins after daily

    def _add_additional_field_row(self) -> None:
        """Add a new name/source row to the additional fields container."""
        # Create widgets for this row
        name_edit = WidgetFactory.create_line_edit(placeholder="Field Name")
        name_edit.setMaximumWidth(200)

        source_edit = WidgetFactory.create_line_edit(placeholder="Data Source")
        source_edit.setMaximumWidth(200)

        remove_button = QPushButton("-")
        remove_button.setMaximumWidth(30)
        remove_button.setStyleSheet("QPushButton { color: red; font-weight: bold; }")

        # Create layout for this row
        row_layout = QHBoxLayout()
        row_layout.addWidget(QLabel("Name:"))
        row_layout.addWidget(name_edit)
        row_layout.addWidget(QLabel("Source:"))
        row_layout.addWidget(source_edit)
        row_layout.addWidget(remove_button)
        row_layout.addStretch()

        # Create container widget for this row
        row_widget = QWidget()
        row_widget.setLayout(row_layout)

        # Store references to the widgets
        row_data = {
            "widget": row_widget,
            "name_edit": name_edit,
            "source_edit": source_edit,
            "remove_button": remove_button,
        }

        # Connect remove button
        remove_button.clicked.connect(lambda checked, row=row_data: self._remove_additional_field_row(row))

        # Connect name field to auto-generate source field
        name_edit.textChanged.connect(lambda text, source=source_edit: self._auto_generate_source(text, source))

        # Add to our tracking list
        self.additional_field_rows.append(row_data)

        # Insert the row before the add button and stretch
        insert_index = self.additional_fields_layout.count() - 2  # Before button and stretch
        self.additional_fields_layout.insertWidget(insert_index, row_widget)

    def _auto_generate_source(self, name_text: str, source_edit: QLineEdit) -> None:
        """Auto-generate source field based on name field input.

        Args:
            name_text (str): The text from the name field.
            source_edit (QLineEdit): The source field to populate.
        """
        # Only auto-generate if source field is empty (don't override manual edits)
        if source_edit.text().strip():
            return

        if not name_text.strip():
            source_edit.clear()
            return

        # Hardcoded special patterns
        special_patterns = {
            "payment date timestamp": "payment_date_ts",
            "account number": "external_account_id",
            "amount": "value",
        }

        name_lower = name_text.lower().strip()

        # Check for special patterns first
        if name_lower in special_patterns:
            source_edit.setText(special_patterns[name_lower])
        else:
            # Standard pattern: convert to snake_case
            # Replace spaces with underscores and convert to lowercase
            source_text = name_text.strip().lower().replace(" ", "_")
            # Remove any special characters except underscores
            import re

            source_text = re.sub(r"[^a-z0-9_]", "", source_text)
            source_edit.setText(source_text)

    def _remove_additional_field_row(self, row_data: dict[str, QWidget]) -> None:
        """Remove a specific additional field row.

        Args:
            row_data (dict[str, QWidget]): Row data containing widget references.
        """
        if row_data in self.additional_field_rows:
            # Remove from layout and delete widget
            self.additional_fields_layout.removeWidget(row_data["widget"])
            row_data["widget"].deleteLater()

            # Remove from tracking list
            self.additional_field_rows.remove(row_data)

    def _clear_additional_fields(self) -> None:
        """Clear all additional field rows."""
        # Remove all rows
        for row_data in self.additional_field_rows[:]:  # Use slice to avoid modification during iteration
            self._remove_additional_field_row(row_data)

        # Uncheck the checkbox
        self.additional_fields_check.setChecked(False)
        self.additional_fields_container.setVisible(False)

    def _on_additional_fields_toggled(self, checked: bool) -> None:
        """Handle additional fields checkbox toggle.

        Args:
            checked (bool): Whether the checkbox is checked.
        """
        self.additional_fields_container.setVisible(checked)

        # Add initial row if checked and no rows exist
        if checked and not self.additional_field_rows:
            self._add_additional_field_row()

    def _get_additional_fields_data(self) -> list[dict[str, str]]:
        """Get the current additional fields data.

        Returns:
            list[dict[str, str]]: List of {"name": str, "source": str} dictionaries.
        """
        if not self.additional_fields_check.isChecked():
            return []

        additional_fields = []
        for row_data in self.additional_field_rows:
            name_edit = cast(QLineEdit, row_data["name_edit"])
            source_edit = cast(QLineEdit, row_data["source_edit"])

            name = name_edit.text().strip()
            source = source_edit.text().strip()

            # Only include rows with both name and source filled
            if name and source:
                additional_fields.append({"name": name, "source": source})

        return additional_fields

    def _add_row_with_tooltip(self, form_layout: QFormLayout, label_text: str, widget: QWidget | Any) -> None:
        """Add a row to the form layout with an optional tooltip on the label.

        Args:
            form_layout (QFormLayout): The layout to add the row to.
            label_text (str): The text for the label in the row.
            widget (QWidget | Any): The widget or layout for the field in the row.
        """
        label = QLabel(label_text)
        tooltip_text = TOOLTIPS.get(label_text)
        if tooltip_text:
            label.setToolTip(tooltip_text)

        # Store label references for visibility toggling
        self._store_label_reference(label_text, label)
        form_layout.addRow(label, widget)

    def _store_label_reference(self, label_text: str, label: QLabel) -> None:
        """Store label references for visibility toggling."""
        # Map of label texts to attribute names
        label_mapping = {
            "Day of Month:": "day_of_month_label",
            "FTP Filepath:": "ftp_filepath_label",
            "Email Subject:": "email_subject_label",
            "Add Ons:": "addons_label",
            "Email Base Name:": "email_base_name_label",
            "Daily Schedule Time (HH:mm):": "daily_schedule_time_label",
            "Monthly Schedule Time (HH:mm):": "monthly_schedule_time_label",
            "Monthly Day of Month:": "monthly_day_of_month_label",
            "Date With Transactions:": "date_with_transactions_label",
            "Daily Report Name:": "daily_report_name_label",
            "Monthly Report Name:": "monthly_report_name_label",
            "Add Additional Fields:": "additional_fields_label",
        }

        # Advanced remittance labels
        advanced_labels = [
            "Cancellation:",
            "Chargeback:",
            "Failure:",
            "Refund:",
            "Same Day Cancellation:",
            "Same Day Failure:",
            "Same Day Refund:",
            "Success:",
        ]

        if label_text in label_mapping:
            setattr(self, label_mapping[label_text], label)
        elif label_text in advanced_labels:
            if not hasattr(self, "advanced_remittance_labels"):
                self.advanced_remittance_labels = {}
            self.advanced_remittance_labels[label_text] = label

    def _setup_layout(self) -> None:
        """Setup the main layout with scrollable form area.

        The layout consists of three main sections:
        1. Scrollable form area (top) - contains all form fields with scroll bars when needed
        2. Log area (middle) - draggable height, always visible
        3. Bottom controls (bottom) - fixed, always visible

        This allows users to resize the window smaller while maintaining access to all fields.
        """
        # Main layout for the entire window
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create scrollable area for the form
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Create the form widget that will go inside the scroll area
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        form_layout.setContentsMargins(10, 10, 10, 10)
        form_layout.setSpacing(8)

        # Add all form fields
        self._add_form_fields(form_layout)

        # Set the form widget as the scroll area's widget
        scroll_area.setWidget(form_widget)

        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Vertical)
        splitter.addWidget(scroll_area)
        splitter.addWidget(self.log_area)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 0)

        # Set initial sizes (form area larger than log area)
        splitter.setSizes([600, 150])

        main_layout.addWidget(splitter, 1)

        # Bottom controls
        bottom_controls_layout = QHBoxLayout()
        bottom_controls_layout.setSpacing(20)
        bottom_controls_layout.setContentsMargins(0, 10, 0, 0)

        # Left side - checkboxes and run report section
        left_controls = QVBoxLayout()
        left_controls.setSpacing(10)
        left_controls.addWidget(self.json_only_check)
        left_controls.addWidget(self.overwrite_check)

        # Separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("QFrame { color: #e0e0e0; }")
        left_controls.addWidget(separator)

        # Run Report section
        left_controls.addWidget(self.run_report_check)

        # Environment selection (initially hidden)
        left_controls.addWidget(self.environment_group)

        # Report Date field (initially hidden) - Multi-date calendar
        report_date_layout = QVBoxLayout()
        report_date_layout.setContentsMargins(20, 0, 0, 0)
        report_date_label = QLabel("Report Dates:")
        report_date_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        report_date_layout.addWidget(report_date_label)
        report_date_layout.addWidget(self.run_report_date_calendar)
        report_date_layout.addWidget(self.auto_find_date_run_report_check)

        report_date_widget = QWidget()
        report_date_widget.setLayout(report_date_layout)
        left_controls.addWidget(report_date_widget)

        # Report Name field with auto-populate (initially hidden)
        report_name_layout = QHBoxLayout()
        report_name_layout.setContentsMargins(20, 0, 0, 0)
        report_name_layout.addWidget(self.run_report_search_button)
        report_name_layout.addWidget(self.run_report_name_edit, 1)
        report_name_layout.addWidget(self.run_report_auto_populate_check)

        report_name_widget = QWidget()
        report_name_widget.setLayout(report_name_layout)
        left_controls.addWidget(report_name_widget)

        # Splitter for drag-resize (tiny header spacer → scroll list → run button)
        self.run_report_splitter = QSplitter(Qt.Orientation.Vertical)
        self.run_report_splitter.setHandleWidth(4)

        # Header spacer with pop-out button
        header_widget = QWidget()
        header_widget.setFixedHeight(28)  # ensure space for 24px button
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 4, 0)
        header_layout.addStretch(1)
        # Clear-all (red X) button
        self.clear_all_button = WidgetFactory.create_icon_button("✖", tooltip="Clear all selected reports")
        self.clear_all_button.setVisible(False)
        self.clear_all_button.setStyleSheet(
            "QPushButton { color: #c0392b; border: none; }"
            "QPushButton:hover { background-color: #f8d7da; border-radius: 4px; }"
        )
        header_layout.addWidget(self.clear_all_button)

        # Pop-out button (arrow)
        self.pop_out_button = WidgetFactory.create_icon_button("↗", tooltip="Pop out list window")
        self.pop_out_button.setVisible(False)  # hidden until at least one report selected
        header_layout.addWidget(self.pop_out_button)

        self.run_report_splitter.addWidget(header_widget)
        self.run_report_splitter.addWidget(self.selected_reports_scroll_area)
        self.run_report_splitter.addWidget(self._run_buttons_container)
        self.run_report_splitter.setSizes([28, 120, 40])

        # Hide the grab-handle visuals (keep drag behaviour)
        self.run_report_splitter.setStyleSheet("QSplitter::handle { image: none; background: transparent; }")

        left_controls.addWidget(self.run_report_splitter)

        # Store references to the conditional widgets for visibility toggling
        self.report_date_widget = report_date_widget
        self.report_name_widget = report_name_widget
        self.report_button_widget = self._run_buttons_container
        self.report_date_label = report_date_label
        self.report_name_label = self.run_report_name_edit

        # Initially hide the conditional fields
        self.report_date_widget.setVisible(False)
        self.report_name_widget.setVisible(False)
        self.report_button_widget.setVisible(False)

        # Show/hide the scroll area if any reports are selected
        self.selected_reports_scroll_area.setVisible(False)
        # Ensure splitter resizes appropriately (maintain 3 widgets)
        sizes = self.run_report_splitter.sizes()
        new_list_size = min(300, sizes[1] + 20)
        self.run_report_splitter.setSizes([sizes[0], new_list_size, sizes[2]])

        bottom_controls_layout.addLayout(left_controls)
        bottom_controls_layout.addStretch(1)
        bottom_controls_layout.addWidget(self.create_button, 0, Qt.AlignmentFlag.AlignBottom)

        main_layout.addLayout(bottom_controls_layout)
        self.setLayout(main_layout)

    def _add_form_fields(self, form_layout: QFormLayout) -> None:
        """Add all form fields to the layout."""
        # Delivery Frequency
        frequency_layout = QHBoxLayout()
        frequency_layout.addWidget(self.daily_frequency_check)
        frequency_layout.addWidget(self.monthly_frequency_check)
        frequency_layout.addStretch()
        self._add_row_with_tooltip(form_layout, "Delivery Frequency:", frequency_layout)

        # Basic information
        self._add_row_with_tooltip(form_layout, "App Name:", self.app_name_edit)
        self._add_row_with_tooltip(form_layout, "Report Name:", self.report_name_edit)

        # Daily Report Name row with search button
        daily_rn_layout = QHBoxLayout()
        daily_rn_layout.addWidget(self.daily_report_search_button)
        daily_rn_layout.addWidget(self.daily_report_name_edit)
        daily_rn_layout.addStretch()
        self._add_row_with_tooltip(form_layout, "Daily Report Name:", daily_rn_layout)
        if hasattr(self, "daily_report_name_label"):
            self.daily_report_name_label.setVisible(False)
        self.daily_report_name_edit.setVisible(False)

        self._add_row_with_tooltip(form_layout, "Monthly Report Name:", self.monthly_report_name_edit)
        if hasattr(self, "monthly_report_name_label"):
            self.monthly_report_name_label.setVisible(False)
        self.monthly_report_name_edit.setVisible(False)

        # Service configuration
        self._add_row_with_tooltip(form_layout, "SOID:", self.soid_edit)
        self._add_row_with_tooltip(form_layout, "Service Offering Type:", self.service_offering_combo)

        # Add Ons row (initially hidden)
        addons_layout = QHBoxLayout()
        addons_layout.addWidget(self.ivr_addon_check)
        addons_layout.addWidget(self.integrated_pos_addon_check)
        addons_layout.addStretch()
        self._add_row_with_tooltip(form_layout, "Add Ons:", addons_layout)
        if hasattr(self, "addons_label"):
            self.addons_label.setVisible(False)
        self.ivr_addon_check.setVisible(False)
        self.integrated_pos_addon_check.setVisible(False)

        # Additional Fields section (before Client Time Zone)
        self._add_row_with_tooltip(form_layout, "Add Additional Fields:", self.additional_fields_check)
        self._add_row_with_tooltip(form_layout, "", self.additional_fields_container)

        self._add_row_with_tooltip(form_layout, "Client Time Zone:", self.timezone_combo)

        # Remittance Type
        radio_layout = QHBoxLayout()
        radio_layout.addWidget(self.combined_radio)
        radio_layout.addWidget(self.return_radio)
        radio_layout.addStretch()
        self._add_row_with_tooltip(form_layout, "Remittance Type:", radio_layout)

        # Advanced Remittance
        self._add_row_with_tooltip(form_layout, "", self.advanced_remittance_check)
        for key, combo in self.advanced_remittance_widgets.items():
            self._add_row_with_tooltip(form_layout, f"{key}:", combo)

        # Delivery configuration
        delivery_layout = QHBoxLayout()
        delivery_layout.addWidget(self.email_check)
        delivery_layout.addWidget(self.ftp_check)
        delivery_layout.addStretch()
        self._add_row_with_tooltip(form_layout, "Delivery Config:", delivery_layout)

        self._add_row_with_tooltip(form_layout, "Email Subject:", self.email_subject_edit)
        if hasattr(self, "email_subject_label"):
            self.email_subject_label.setVisible(False)
        self.email_subject_edit.setVisible(False)

        self._add_row_with_tooltip(form_layout, "FTP Filepath:", self.ftp_filepath_edit)
        if hasattr(self, "ftp_filepath_label"):
            self.ftp_filepath_label.setVisible(False)
        self.ftp_filepath_edit.setVisible(False)

        self._add_row_with_tooltip(form_layout, "FTP/SFTP Credentials:", self.ftp_credential_combo)

        # Email Base fields
        self._add_row_with_tooltip(form_layout, "Email Base (Google Group):", self.email_base_auto_check)
        self._add_row_with_tooltip(form_layout, "Email Base Name:", self.email_base_manual_edit)
        if hasattr(self, "email_base_name_label"):
            self.email_base_name_label.setVisible(False)

        # Date With Transactions field (initially hidden)
        date_with_transactions_layout = QHBoxLayout()
        date_with_transactions_layout.addWidget(self.date_with_transactions_edit)
        date_with_transactions_layout.addWidget(self.auto_find_date_monthly_check)
        date_with_transactions_layout.addStretch()
        self._add_row_with_tooltip(form_layout, "Date With Transactions:", date_with_transactions_layout)
        if hasattr(self, "date_with_transactions_label"):
            self.date_with_transactions_label.setVisible(False)
        self.date_with_transactions_edit.setVisible(False)
        self.auto_find_date_monthly_check.setVisible(False)

        # Separator
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        form_layout.addRow(line)

        # Schedule fields
        self._add_row_with_tooltip(form_layout, "Daily Schedule Time (HH:mm):", self.daily_schedule_time_edit)
        self._add_row_with_tooltip(form_layout, "Monthly Schedule Time (HH:mm):", self.monthly_schedule_time_edit)
        self._add_row_with_tooltip(form_layout, "Monthly Day of Month:", self.monthly_day_of_month_spinbox)

        # Show Available Times button (always visible)
        form_layout.addRow("", self.show_available_times_button)

    def _connect_signals(self) -> None:
        """Connect widget signals to their handlers."""
        self.daily_frequency_check.toggled.connect(self._on_frequency_selection_changed)
        self.monthly_frequency_check.toggled.connect(self._on_frequency_selection_changed)
        self.ftp_check.toggled.connect(self._on_ftp_toggled)
        self.email_check.toggled.connect(self._on_email_toggled)
        self.service_offering_combo.currentTextChanged.connect(self._on_service_offering_changed)
        self.advanced_remittance_check.toggled.connect(self._on_advanced_remittance_toggled)
        self.email_base_auto_check.toggled.connect(self._on_email_base_auto_toggled)
        self.report_name_edit.textChanged.connect(self._on_report_name_changed)
        self.ftp_filepath_edit.textChanged.connect(self._on_ftp_filepath_changed)
        self.create_button.clicked.connect(self.run_file_creation)
        self.show_available_times_button.clicked.connect(self._on_show_available_times_clicked)
        self.run_report_button.clicked.connect(self._on_run_report_clicked)
        self.run_report_search_button.clicked.connect(self._on_run_report_search_clicked)
        self.cancel_all_button.clicked.connect(self._on_cancel_all_clicked)

        # Additional fields signals
        self.additional_fields_check.toggled.connect(self._on_additional_fields_toggled)
        self.add_field_button.clicked.connect(self._add_additional_field_row)

        # Run Report signals
        self.run_report_check.toggled.connect(self._on_run_report_check_toggled)
        self.run_report_auto_populate_check.toggled.connect(self._on_run_report_auto_populate_toggled)

        # Auto-find date signals
        self.auto_find_date_monthly_check.toggled.connect(self._on_auto_find_date_monthly_toggled)
        self.auto_find_date_run_report_check.toggled.connect(self._on_auto_find_date_run_report_toggled)

        # Connect to update calendar visibility when auto-find checkbox changes
        self.auto_find_date_run_report_check.toggled.connect(self._update_calendar_visibility)

        # Daily report search
        self.daily_report_search_button.clicked.connect(self._on_daily_report_search_clicked)

        # Pop-out button
        self.pop_out_button.clicked.connect(self._on_pop_out_clicked)
        self.clear_all_button.clicked.connect(self._on_clear_all_clicked)

    def _on_frequency_selection_changed(self) -> None:
        """Show or hide schedule-related fields based on frequency checkbox selections."""
        daily_selected = self.daily_frequency_check.isChecked()
        monthly_selected = self.monthly_frequency_check.isChecked()
        both_selected = daily_selected and monthly_selected

        # Daily schedule visibility
        if hasattr(self, "daily_schedule_time_label"):
            self.daily_schedule_time_label.setVisible(daily_selected)
        self.daily_schedule_time_edit.setVisible(daily_selected)

        # Monthly schedule visibility
        if hasattr(self, "monthly_schedule_time_label"):
            self.monthly_schedule_time_label.setVisible(monthly_selected)
        self.monthly_schedule_time_edit.setVisible(monthly_selected)

        if hasattr(self, "monthly_day_of_month_label"):
            self.monthly_day_of_month_label.setVisible(monthly_selected)
        self.monthly_day_of_month_spinbox.setVisible(monthly_selected)

        # Date With Transactions field visibility
        if hasattr(self, "date_with_transactions_label"):
            self.date_with_transactions_label.setVisible(monthly_selected)
        self.date_with_transactions_edit.setVisible(monthly_selected)
        self.auto_find_date_monthly_check.setVisible(monthly_selected)

        # Daily Report Name field visibility (only when Monthly is selected but Daily is not)
        if hasattr(self, "daily_report_name_label"):
            self.daily_report_name_label.setVisible(monthly_selected and not daily_selected)
        self.daily_report_name_edit.setVisible(monthly_selected and not daily_selected)

        # Monthly Report Name field visibility (only when both Daily and Monthly are selected)
        if hasattr(self, "monthly_report_name_label"):
            self.monthly_report_name_label.setVisible(both_selected)
        self.monthly_report_name_edit.setVisible(both_selected)

        # Auto-populate monthly report name when both are selected
        if both_selected:
            self._update_monthly_report_name()

        # Show/hide search button similarly
        self.daily_report_search_button.setVisible(monthly_selected and not daily_selected)

    def _on_ftp_toggled(self, checked: bool) -> None:
        """Show or hide the FTP Filepath field based on the FTP checkbox state."""
        VisibilityManager.create_toggle_handler(
            widgets=[self.ftp_filepath_edit], labels=[getattr(self, "ftp_filepath_label", None)]
        )(checked)

    def _on_email_toggled(self, checked: bool) -> None:
        """Show or hide the Email Subject field based on the Email checkbox state."""
        VisibilityManager.create_toggle_handler(
            widgets=[self.email_subject_edit], labels=[getattr(self, "email_subject_label", None)]
        )(checked)

    def _on_service_offering_changed(self, text: str) -> None:
        """Show or hide the Add Ons row and Additional Fields based on Service Offering selection."""
        # Show Add Ons for all service types except Non-Integrated POS and Non-Integrated Form
        show_addons = text not in ["Non-Integrated POS", "Non-Integrated Form"]
        if hasattr(self, "addons_label"):
            self.addons_label.setVisible(show_addons)
        self.ivr_addon_check.setVisible(show_addons)
        self.integrated_pos_addon_check.setVisible(show_addons)

        # If hiding, clear the selections
        if not show_addons:
            self.ivr_addon_check.setChecked(False)
            self.integrated_pos_addon_check.setChecked(False)

        # Show Additional Fields for all service types except Non-Integrated Form, Non-Integrated POS, and SDK
        show_additional_fields = text not in ["Non-Integrated Form", "Non-Integrated POS", "SDK"]
        if hasattr(self, "additional_fields_label"):
            self.additional_fields_label.setVisible(show_additional_fields)
        self.additional_fields_check.setVisible(show_additional_fields)

        # If hiding or changing service type, clear additional fields data
        if not show_additional_fields or text != getattr(self, "_previous_service_offering", ""):
            self._clear_additional_fields()

        # Store current selection for change detection
        self._previous_service_offering = text

    def _update_advanced_remittance_visibility(self, checked: bool) -> None:
        """Helper to show or hide advanced remittance widgets without recursion."""
        widgets = list(self.advanced_remittance_widgets.values())
        labels = list(getattr(self, "advanced_remittance_labels", {}).values())
        VisibilityManager.create_toggle_handler(widgets, labels)(checked)

    def _on_advanced_remittance_toggled(self, checked: bool) -> None:
        """Qt slot for the Advanced Remittance checkbox."""
        # Update visibility
        self._update_advanced_remittance_visibility(checked)

        # Reset dropdowns if unchecked
        if not checked:
            for combo in self.advanced_remittance_widgets.values():
                combo.setCurrentText("default")

    def _on_email_base_auto_toggled(self, checked: bool) -> None:
        """Show or hide the manual email base input field."""
        is_manual = not checked
        VisibilityManager.create_toggle_handler(
            widgets=[self.email_base_manual_edit], labels=[getattr(self, "email_base_name_label", None)]
        )(is_manual)

    def _on_report_name_changed(self) -> None:
        """Update the monthly report name when the main report name changes."""
        # Only update if both frequencies are selected
        if self.daily_frequency_check.isChecked() and self.monthly_frequency_check.isChecked():
            self._update_monthly_report_name()

        # Update run report name if auto-populate is enabled
        current_name = self.report_name_edit.text().strip()
        if self.run_report_auto_populate_check.isChecked():
            self.run_report_name_edit.setText(current_name)

        # Reset button state when report name changes
        if not self.report_runner.is_running:
            self.button_manager.set_state("normal")

    def _update_monthly_report_name(self) -> None:
        """Auto-generate the monthly report name from the main report name."""
        main_report_name = self.report_name_edit.text().strip()
        if main_report_name and DAILY_REPORT_SUFFIX in main_report_name:
            monthly_report_name = main_report_name.replace(DAILY_REPORT_SUFFIX, MONTHLY_REPORT_SUFFIX)
            self.monthly_report_name_edit.setText(monthly_report_name)

    def _on_ftp_filepath_changed(self) -> None:
        """Remove leading forward slashes from FTP filepath input."""
        current_text = self.ftp_filepath_edit.text()
        if current_text.startswith("/"):
            cleaned_text = current_text.lstrip("/")
            self.ftp_filepath_edit.setText(cleaned_text)

    def _on_show_available_times_clicked(self) -> None:
        """Handle the Show Available Times button click."""
        self._log_message("Analyzing available schedule time slots...", "blue")

        try:
            # Create analyzer instance
            analyzer = ScheduleSlotAnalyzer()

            # Get formatted available times
            result = analyzer.get_available_times_for_gui()

            # Display result in log area
            self._log_message(result, "black")

        except Exception as e:
            error_msg = f"Error analyzing schedule slots: {e!s}"
            self._log_message(error_msg, "red")

    def _log_message(self, message: str, color_name: str) -> None:
        """Append a timestamped message with specified color to the log area."""
        timestamp = QTime.currentTime().toString("HH:mm:ss")
        formatted_message = message.replace("\n", "<br>")
        html_message = f'<font color="{color_name}">[{timestamp}] {formatted_message}</font>'
        self.log_area.append(html_message)

    def _on_run_report_clicked(self) -> None:
        """Handle the Run Report button click for one or many reports across multiple dates."""
        # Determine reports to run
        reports_to_run = (
            list(self._selected_reports) if self._selected_reports else [self.run_report_name_edit.text().strip()]
        )

        if not reports_to_run or not reports_to_run[0]:
            self.validator.show_error("Please specify at least one report name to run.")
            return

        # Get dates - either from per-report storage or main calendar
        if self.auto_find_date_run_report_check.isChecked():
            # Use per-report dates when auto-find is enabled
            report_date_pairs = []
            missing_dates = []

            for report_name in reports_to_run:
                date_str = self._report_dates.get(report_name)
                if date_str:
                    report_date_pairs.append((report_name, date_str))
                else:
                    missing_dates.append(report_name)

            if missing_dates:
                self.validator.show_error(
                    f"Missing dates for reports: {', '.join(missing_dates)}. "
                    "Please use auto-find or set dates manually."
                )
                return

            if not report_date_pairs:
                self.validator.show_error("No report dates available. Please use auto-find or set dates manually.")
                return

            # Create execution matrix from per-report dates
            execution_matrix = report_date_pairs

        else:
            # Use main calendar dates when auto-find is disabled
            selected_dates = self.run_report_date_calendar.get_selected_dates_as_strings()

            if not selected_dates:
                self.validator.show_error("Please select at least one report date.")
                return

            # Create execution matrix (all reports × all dates)
            execution_matrix = [(report, date) for report in reports_to_run for date in selected_dates]

        self.button_manager.set_state("running")
        self.cancel_all_button.setVisible(True)

        color_palette = [
            "black",
            "purple",
            "teal",
            "brown",
            "navy",
            "darkgreen",
            "maroon",
        ]

        # Log the execution plan
        self._log_message(f"Starting {len(execution_matrix)} report executions", "blue")
        for report_name, date_str in execution_matrix:
            self._log_message(f"  • {report_name} on {date_str}", "blue")

        # Process each report/date combination
        for idx, (report_name, date_str) in enumerate(execution_matrix):
            # Create unique identifier for this report/date combination
            execution_id = f"{report_name}@{date_str}"

            # Get selected environment
            environment = "dev" if self.dev_radio.isChecked() else "staging"

            # Create RunReportData with multiple dates support
            run_data = RunReportData(
                report_name=report_name,
                report_date=date_str,  # Keep for backward compatibility
                report_dates=[date_str],  # New multi-date field
                auto_populate_name=False,
                environment=environment,
            )

            valid, err = validate_run_report_inputs(run_data)
            if not valid:
                self._log_message(f"[{execution_id}] Validation failed: {err}", "red")
                continue

            success, err, env_vars = self.report_runner.prepare_report_execution(run_data)
            if not success:
                self._log_message(f"[{execution_id}] {err}", "red")
                continue

            command = self.report_runner.build_command(env_vars)

            color = color_palette[idx % len(color_palette)]
            self._report_colors[execution_id] = color
            self._log_message(f"[{execution_id}] Starting...", color)

            process = QProcess()
            process.setWorkingDirectory(str(PROJECT_ROOT))

            # Connect output handlers with closures using execution_id
            process.readyReadStandardOutput.connect(
                lambda eid=execution_id, p=process: self._handle_process_stream(eid, p, "stdout")
            )
            process.readyReadStandardError.connect(
                lambda eid=execution_id, p=process: self._handle_process_stream(eid, p, "stderr")
            )
            process.finished.connect(
                lambda exit_code, _status, eid=execution_id: self._process_finished_individual(eid, exit_code)
            )

            self._report_processes[execution_id] = process
            process.start("bash", ["-c", command])

        if not self._report_processes:
            # All failed upfront
            self.button_manager.set_state("error")
            self.cancel_all_button.setVisible(False)

    def _handle_process_stream(self, execution_id: str, process: QProcess, stream: str) -> None:
        """Handle stdout/stderr for a given process.

        Args:
            execution_id (str): Unique identifier for this report/date combination (format: "report@date").
            process (QProcess): The process generating the output.
            stream (str): Type of stream ("stdout" or "stderr").
        """
        if stream == "stdout":
            qbyte_array = process.readAllStandardOutput()
        else:
            qbyte_array = process.readAllStandardError()

        # QByteArray.data() returns bytes, but we cast to satisfy type checker.
        raw_bytes = cast(bytes, qbyte_array.data())
        text = raw_bytes.decode().strip()
        if text:
            color = self._report_colors.get(execution_id, "black")
            self._log_message(f"[{execution_id}] {text}", color)

    def _process_finished_individual(self, execution_id: str, exit_code: int) -> None:
        """Callback when a single report process finishes.

        Args:
            execution_id (str): Unique identifier for this report/date combination (format: "report@date").
            exit_code (int): Exit code of the finished process.
        """
        color = self._report_colors.get(execution_id, "black")
        if exit_code == 0:
            self._log_message(f"[{execution_id}] completed successfully.", color)
        else:
            self._log_message(f"[{execution_id}] failed with exit code {exit_code}.", "red")

        # Remove from active
        self._report_processes.pop(execution_id, None)

        if not self._report_processes:
            self.button_manager.set_state("normal")
            self.cancel_all_button.setVisible(False)
            self._log_message("All report processes finished.", "green")

    def _on_run_report_check_toggled(self, checked: bool) -> None:
        """Handle the Run Report checkbox toggle.

        Args:
            checked (bool): Whether the checkbox is checked.
        """
        # Show/hide the conditional fields
        self.environment_group.setVisible(checked)
        self.report_date_widget.setVisible(checked)
        self.report_name_widget.setVisible(checked)
        self.report_button_widget.setVisible(checked)

        # Update auto-populate state if enabled
        if checked and self.run_report_auto_populate_check.isChecked():
            current_name = self.report_name_edit.text().strip()
            self.run_report_name_edit.setText(current_name)

        # Show/hide the scroll area if any reports are selected
        self.selected_reports_scroll_area.setVisible(checked and bool(self._selected_reports))

    def _on_run_report_auto_populate_toggled(self, checked: bool) -> None:
        """Handle the Run Report auto-populate checkbox toggle.

        Args:
            checked (bool): Whether auto-populate is checked.
        """
        if checked:
            # Auto-populate from main form
            current_name = self.report_name_edit.text().strip()
            self.run_report_name_edit.setText(current_name)
            self.run_report_name_edit.setReadOnly(True)
            self.run_report_name_edit.setStyleSheet("""
                QLineEdit {
                    background-color: #f8f9fa;
                    color: #6c757d;
                }
            """)
        else:
            # Enable manual editing
            self.run_report_name_edit.setReadOnly(False)
            self.run_report_name_edit.setStyleSheet("")

    def _on_run_report_name_text_changed(self, text: str) -> None:
        """Handle text changes in the run report name field.

        Args:
            text (str): The current text in the field.
        """
        auto_resize_text_field(self.run_report_name_edit, text)

    def _on_report_dates_changed(self) -> None:
        """Handle changes in the selected report dates."""
        # Reset button state when dates change (unless currently running)
        if not self.report_runner.is_running:
            self.button_manager.set_state("normal")

        # If auto-find is not checked, update all report dates from main calendar
        if not self.auto_find_date_run_report_check.isChecked():
            selected_dates = self.run_report_date_calendar.get_selected_dates_as_strings()
            if selected_dates and self._selected_reports:
                # Use the first selected date for all reports when using main calendar
                first_date = selected_dates[0]
                for report_name in self._selected_reports:
                    self._report_dates[report_name] = first_date
                    self._update_report_date_display(report_name, first_date)
                self._log_message(f"Updated all report dates to: {first_date}", "blue")

    def _on_run_report_search_clicked(self) -> None:
        """Open the report search dialog and populate the selected name."""
        dialog = ReportSearchDialog(self)
        if dialog.exec():
            selected_names = dialog.get_selected_report_names()
            if selected_names:
                self.run_report_auto_populate_check.setChecked(False)
                self._on_run_report_auto_populate_toggled(False)

                for name in selected_names:
                    self._add_selected_report(name)

    def _add_selected_report(self, report_name: str) -> None:
        """Display *report_name* in the selected reports container."""
        if report_name in self._selected_reports:
            return

        row_layout = QHBoxLayout()
        row_layout.setContentsMargins(2, 0, 2, 0)
        row_layout.setSpacing(4)

        # Report name label
        label = QLabel(report_name)
        label.setStyleSheet("font-size: 11px;")

        # Calendar button for per-report date selection
        calendar_btn = WidgetFactory.create_icon_button("📅", tooltip="Set date for this report")
        calendar_btn.setFixedSize(20, 16)
        calendar_btn.clicked.connect(lambda _=False, name=report_name: self._open_report_date_picker(name))

        # Date display label (initially empty)
        date_label = QLabel("")
        date_label.setStyleSheet("font-size: 10px; color: #666; font-style: italic;")
        date_label.setMinimumWidth(70)

        # Remove button
        remove_btn = WidgetFactory.create_icon_button("x", tooltip="Remove")
        remove_btn.setFixedSize(16, 16)
        remove_btn.clicked.connect(lambda _=False, name=report_name: self._remove_selected_report(name))

        row_layout.addWidget(label)
        row_layout.addWidget(calendar_btn)
        row_layout.addWidget(date_label)
        row_layout.addStretch(1)
        row_layout.addWidget(remove_btn)

        row_widget = QWidget()
        row_widget.setLayout(row_layout)

        # Store and display
        self.selected_reports_layout.addWidget(row_widget)
        self._selected_reports.add(report_name)
        self._selected_report_rows[report_name] = row_widget
        self._report_date_buttons[report_name] = calendar_btn

        # Store reference to date label for updates
        setattr(row_widget, '_date_label', date_label)

        self.selected_reports_scroll_area.setVisible(True)
        self.pop_out_button.setVisible(True)
        self.clear_all_button.setVisible(True)
        self._update_selected_reports_width()

    def _remove_selected_report(self, report_name: str) -> None:
        """Remove *report_name* from the selected list display and state."""
        widget = self._selected_report_rows.pop(report_name, None)
        if widget:
            self.selected_reports_layout.removeWidget(widget)
            widget.deleteLater()

        # Clean up per-report date storage
        self._selected_reports.discard(report_name)
        self._report_dates.pop(report_name, None)
        self._report_date_buttons.pop(report_name, None)

        if not self._selected_reports:
            self.selected_reports_scroll_area.setVisible(False)
            self.pop_out_button.setVisible(False)
            self.clear_all_button.setVisible(False)
            # Reset min width to default
            self.run_report_splitter.setMinimumWidth(0)
            self.selected_reports_scroll_area.setMinimumWidth(0)
            self.selected_reports_container.setMinimumWidth(0)
            self.run_report_splitter.widget(0).setMinimumWidth(0)
        else:
            # Recompute width after removal
            self._update_selected_reports_width()

    def _on_cancel_all_clicked(self) -> None:
        """Terminate all running report processes and reset state."""
        for process in list(self._report_processes.values()):
            if process.state() != QProcess.ProcessState.NotRunning:
                process.kill()
        self._report_processes.clear()
        self.cancel_all_button.setVisible(False)
        self.button_manager.set_state("error")
        self._log_message("All running reports cancelled.", "red")

    def _collect_run_report_data(self) -> RunReportData:
        """Collect run report form data into a RunReportData object.

        Returns:
            RunReportData: The collected run report data.
        """
        auto_populate = self.run_report_auto_populate_check.isChecked()
        report_name = (
            self.report_name_edit.text().strip() if auto_populate else self.run_report_name_edit.text().strip()
        )

        # Get selected dates from the multi-date calendar
        selected_dates = self.run_report_date_calendar.get_selected_dates_as_strings()

        # Use first date for backward compatibility if available
        first_date = selected_dates[0] if selected_dates else ""

        # Get selected environment
        environment = "dev" if self.dev_radio.isChecked() else "staging"

        return RunReportData(
            report_name=report_name,
            report_date=first_date,  # Keep for backward compatibility
            report_dates=selected_dates,  # New multi-date field
            auto_populate_name=auto_populate,
            environment=environment,
        )

    # ---------------------------------------------------------------------
    # Form-data collection helpers (used by file-creation workflow)
    # ---------------------------------------------------------------------

    def _collect_gui_form_data(self) -> FormData:
        """Gather the entire state of the GUI into a ``FormData`` object."""
        delivery_data = self._collect_delivery_config()
        addon_data = self._collect_addon_config()
        advanced_data = self._collect_advanced_config()
        email_subjects = self._collect_email_subjects()
        schedule_data = self._collect_schedule_config()

        return FormData(
            app_name=self.app_name_edit.text().strip(),
            report_name_input=self.report_name_edit.text().strip(),
            client_timezone=self.timezone_combo.currentText(),
            remittance_type="Return" if self.return_radio.isChecked() else "Combined",
            service_offering_type=self.service_offering_combo.currentText(),
            soid=self.soid_edit.text().strip(),
            **delivery_data,
            **addon_data,
            **advanced_data,
            **email_subjects,
            **schedule_data,
            ftp_credential_val=self.ftp_credential_combo.currentText(),
            overwrite_existing_val=self.overwrite_check.isChecked(),
            email_base_val=self.email_base_manual_edit.text().strip()
            if not self.email_base_auto_check.isChecked()
            else None,
            is_auto_email_base=self.email_base_auto_check.isChecked(),
            create_json_only=self.json_only_check.isChecked(),
        )

    def _collect_delivery_config(self) -> dict[str, Any]:
        """Collect Email/FTP delivery data."""
        delivery_method_str = ""
        if self.email_check.isChecked() and self.ftp_check.isChecked():
            delivery_method_str = "both"
        elif self.email_check.isChecked():
            delivery_method_str = "email"
        elif self.ftp_check.isChecked():
            delivery_method_str = "ftp"

        return {
            "email_selected": self.email_check.isChecked(),
            "ftp_selected": self.ftp_check.isChecked(),
            "delivery_method_str": delivery_method_str,
            "ftp_path_val": self.ftp_filepath_edit.text().strip() if self.ftp_check.isChecked() else None,
        }

    def _collect_addon_config(self) -> dict[str, Any]:
        """Collect add-on and manually-added field information."""
        manual_additional_fields = self._get_additional_fields_data()
        manual_sources = {field["source"].lower() for field in manual_additional_fields}

        addons: list[str] = []
        if self.service_offering_combo.currentText() not in ["Non-Integrated POS", "Non-Integrated Form"]:
            if self.ivr_addon_check.isChecked() and "ivr_confirmation_number" not in manual_sources:
                addons.append("IVR")
            if (
                self.integrated_pos_addon_check.isChecked()
                and "confirmation_number" not in manual_sources
                and "pos_confirmation_number" not in manual_sources
            ):
                addons.append("Integrated POS")

        return {
            "addons": addons,
            "manual_additional_fields": manual_additional_fields,
        }

    def _collect_advanced_config(self) -> dict[str, Any]:
        """Collect advanced remittance selections (if enabled)."""
        advanced_selections_val = None
        if self.advanced_remittance_check.isChecked():
            advanced_selections_val = {
                key: combo.currentText() for key, combo in self.advanced_remittance_widgets.items()
            }
        return {"advanced_selections_val": advanced_selections_val}

    def _collect_email_subjects(self) -> dict[str, Any]:
        """Collect email subjects."""
        email_subject_val = self.email_subject_edit.text().strip()
        daily_email_subject: str | None = None
        monthly_email_subject: str | None = None

        if self.email_check.isChecked() and email_subject_val:
            if self.daily_frequency_check.isChecked():
                daily_email_subject = email_subject_val
                if self.monthly_frequency_check.isChecked():
                    monthly_email_subject = email_subject_val.replace("Remittance DATE", "Monthly Recap DATE")
            elif self.monthly_frequency_check.isChecked():
                monthly_email_subject = email_subject_val

        return {
            "email_subject_val": email_subject_val if email_subject_val else None,
            "daily_email_subject": daily_email_subject,
            "monthly_email_subject": monthly_email_subject,
        }

    def _collect_schedule_config(self) -> dict[str, Any]:
        """Gather schedule-related selections for daily/monthly jobs."""
        return {
            "daily_frequency_selected": self.daily_frequency_check.isChecked(),
            "monthly_frequency_selected": self.monthly_frequency_check.isChecked(),
            "daily_schedule_time": self.daily_schedule_time_edit.time(),
            "monthly_schedule_time": self.monthly_schedule_time_edit.time(),
            "monthly_day_of_month_val": self.monthly_day_of_month_spinbox.value(),
            "date_with_transactions": self.date_with_transactions_edit.text().strip(),
            "daily_report_name": self.daily_report_name_edit.text().strip(),
            "monthly_report_name": self.monthly_report_name_edit.text().strip(),
        }

    # ---------------------------------------------------------------------
    # File-generation helpers
    # ---------------------------------------------------------------------

    def _process_daily_report(self, form_data: FormData) -> tuple[bool, str]:
        """Generate daily-frequency configuration files."""
        self._log_message("Starting Daily report generation...", "blue")
        form_data.report_name = form_data.report_name_input
        return self.config_generator.generate_configuration_files(
            form_data=form_data,
            frequency="daily",
            overwrite_existing=form_data.overwrite_existing_val,
        )

    def _process_monthly_report(self, form_data: FormData) -> tuple[bool, str]:
        """Generate monthly-frequency configuration files."""
        self._log_message("Starting Monthly report generation...", "blue")
        form_data.report_name = (
            form_data.monthly_report_name if form_data.daily_frequency_selected else form_data.report_name_input
        )
        return self.config_generator.generate_configuration_files(
            form_data=form_data,
            frequency="monthly",
            overwrite_existing=form_data.overwrite_existing_val,
        )

    def _show_results(self, overall_success: bool, all_messages: list[str]) -> None:
        """Display a summary dialog after file generation completes."""
        final_message_str = "\n\n".join(all_messages)
        log_color = "green" if overall_success else "red"
        self._log_message(f"Overall Process Summary:\n{final_message_str}", log_color)
        if overall_success:
            QMessageBox.information(self, "Success", final_message_str)
        else:
            QMessageBox.critical(self, "Error / Partial Success", final_message_str)

    # ---------------------------------------------------------------------
    # Public slot - called by the *Create Files* button
    # ---------------------------------------------------------------------

    def run_file_creation(self) -> None:
        """Validate inputs and generate configuration / schedule files."""
        form_data = self._collect_gui_form_data()
        is_valid, error_message = validate_all_inputs(form_data)
        if not is_valid:
            self.validator.show_error(error_message)
            return

        form_data.report_name = form_data.report_name_input
        self._log_message("Generating files...", "black")
        QApplication.processEvents()

        overall_success = True
        all_messages: list[str] = []

        if form_data.daily_frequency_selected:
            success, message = self._process_daily_report(form_data)
            all_messages.append(f"Daily Report Generation:\n{message}")
            overall_success &= success

        if form_data.monthly_frequency_selected:
            success, message = self._process_monthly_report(form_data)
            all_messages.append(f"Monthly Report Generation:\n{message}")
            overall_success &= success

        self._show_results(overall_success, all_messages)

    # ------------------------------------------------------------------
    # Daily report search / auto-populate for Monthly-only workflow
    # ------------------------------------------------------------------

    def _on_daily_report_search_clicked(self) -> None:
        """Open the report search dialog to choose a daily report and auto-populate fields."""
        dialog = ReportSearchDialog(self)
        if not dialog.exec():
            return

        selected_names = dialog.get_selected_report_names()
        if not selected_names:
            return

        daily_report_name = selected_names[0]

        # Ensure selected report appears to be a daily report
        if not daily_report_name.endswith(DAILY_REPORT_SUFFIX):
            self.validator.show_error(
                f"Selected report '{daily_report_name}' does not look like a daily report "
                f"(must end with '{DAILY_REPORT_SUFFIX}')"
            )
            return

        # Populate the read-only Daily Report Name field (still needed for monthly only workflow)
        self.daily_report_name_edit.setText(daily_report_name)

        # Attempt to load snapshot from JSON config
        try:
            snapshot = load_daily_snapshot(daily_report_name)
        except ConfigNotFoundError:
            self.validator.show_error(f"Configuration file not found for {daily_report_name}.")
            return
        except InvalidConfigError as exc:
            self.validator.show_error(f"Failed to read configuration: {exc}")
            return
        except Exception as exc:  # Catch all safety net
            self.validator.show_error(f"Unexpected error loading configuration: {exc}")
            return

        # Apply snapshot values to the form
        self._apply_snapshot_to_form(daily_report_name, snapshot)

    # ------------------------------------------------------------------
    # Snapshot → GUI helper
    # ------------------------------------------------------------------

    def _apply_snapshot_to_form(self, daily_report_name: str, snap: DailyReportSnapshot) -> None:
        """Copy values from *snap* into the GUI widgets."""

        # Store the output_file_name for S3 file lookup and downstream use
        self._daily_output_file_name = snap.output_file_name  # May be None if not present in config

        def _to_monthly_subject(src: str | None) -> str | None:
            """Convert *src* ending in Financial/Remittance DATE → Monthly Recap DATE.

            Keeps original string unchanged if no recognised suffix is present or src is None.
            """
            if src is None:
                return None

            if src.endswith("Financial Remittance DATE"):
                return src[: -len("Financial Remittance DATE")] + "Monthly Recap DATE"
            if src.endswith("Remittance DATE"):
                return src[: -len("Remittance DATE")] + "Monthly Recap DATE"
            # Fallback: swap the first occurrence if present
            if "Remittance DATE" in src:
                return src.replace("Remittance DATE", "Monthly Recap DATE")
            return src

        # Derived monthly name
        monthly_name = daily_report_name.replace(DAILY_REPORT_SUFFIX, MONTHLY_REPORT_SUFFIX)

        # ----- Basic fields -----
        with SignalBlocker(self.app_name_edit):
            self.app_name_edit.setText(snap.app_name)
        with SignalBlocker(self.report_name_edit):
            self.report_name_edit.setText(monthly_name)
        with SignalBlocker(self.soid_edit):
            self.soid_edit.setText(snap.soid)

        # Service offering combo triggers other visibility conditions
        with SignalBlocker(self.service_offering_combo):
            self.service_offering_combo.setCurrentText(snap.service_offering_type)
        self._on_service_offering_changed(snap.service_offering_type)

        # Client timezone
        if snap.client_timezone in TIMEZONE_OPTIONS:
            with SignalBlocker(self.timezone_combo):
                self.timezone_combo.setCurrentText(snap.client_timezone)

        # ----- Delivery configuration -----
        # Email check
        with SignalBlocker(self.email_check):
            self.email_check.setChecked(snap.email_selected)
        self._on_email_toggled(snap.email_selected)
        if snap.email_selected:
            monthly_subject = _to_monthly_subject(snap.email_subject)
            if monthly_subject:
                with SignalBlocker(self.email_subject_edit):
                    self.email_subject_edit.setText(monthly_subject)

        # FTP check
        with SignalBlocker(self.ftp_check):
            self.ftp_check.setChecked(snap.ftp_selected)
        self._on_ftp_toggled(snap.ftp_selected)
        if snap.ftp_selected and snap.ftp_filepath:
            with SignalBlocker(self.ftp_filepath_edit):
                self.ftp_filepath_edit.setText(snap.ftp_filepath)

        # ----- Email base -----
        if snap.email_base:
            with SignalBlocker(self.email_base_auto_check):
                self.email_base_auto_check.setChecked(False)
            self._on_email_base_auto_toggled(False)
            with SignalBlocker(self.email_base_manual_edit):
                self.email_base_manual_edit.setText(snap.email_base)
        else:
            # Use auto-generate
            with SignalBlocker(self.email_base_auto_check):
                self.email_base_auto_check.setChecked(True)
            self._on_email_base_auto_toggled(True)

        # ----- Remittance type & advanced selections -----
        with SignalBlocker(self.combined_radio), SignalBlocker(self.return_radio):
            if snap.remittance_type == "Combined":
                self.combined_radio.setChecked(True)
            else:
                self.return_radio.setChecked(True)

        if snap.advanced_selections is None:
            with SignalBlocker(self.advanced_remittance_check):
                self.advanced_remittance_check.setChecked(False)
            self._update_advanced_remittance_visibility(False)
        else:
            with SignalBlocker(self.advanced_remittance_check):
                self.advanced_remittance_check.setChecked(True)
            # Ensure widgets visible before setting values
            self._update_advanced_remittance_visibility(True)

            # Update combo selections
            for gui_key, combo_widget in self.advanced_remittance_widgets.items():
                desired_value = snap.advanced_selections.get(gui_key, "default")
                if desired_value in [combo_widget.itemText(i) for i in range(combo_widget.count())]:
                    with SignalBlocker(combo_widget):
                        combo_widget.setCurrentText(desired_value)
                else:
                    with SignalBlocker(combo_widget):
                        combo_widget.setCurrentText("default")

        self._log_message(f"Populated fields from {daily_report_name}.json", "green")

    def _on_daily_report_name_editing_finished(self):
        """Handler for when the user manually enters or edits the daily report name field."""
        daily_report_name = self.daily_report_name_edit.text().strip()
        if daily_report_name:
            try:
                snapshot = load_daily_snapshot(daily_report_name)
                self._daily_output_file_name = snapshot.output_file_name
            except Exception:
                self._daily_output_file_name = None

    def get_s3_base_filename(self) -> str:
        """Return the correct S3 base filename for the selected daily report."""
        daily_report_name = self.daily_report_name_edit.text().strip()
        return self._daily_output_file_name or daily_report_name.replace("_financial_remittance", "")

    def _on_pop_out_clicked(self) -> None:
        """Handle pop-out button. Show selected reports in a separate dialog."""
        if hasattr(self, "_selected_reports_dialog") and self._selected_reports_dialog:
            # Already open → bring to front
            self._selected_reports_dialog.raise_()
            self._selected_reports_dialog.activateWindow()
            return

        from PySide6.QtWidgets import QVBoxLayout

        dlg = QDialog(self, Qt.WindowType.Window)
        dlg.setWindowTitle("Selected Reports")

        # Calculate ideal width based on longest report name
        if self._selected_reports:
            fm = self.fontMetrics()
            max_text_px = max(fm.horizontalAdvance(name) for name in self._selected_reports)
        else:
            max_text_px = 200
        ideal_width = max_text_px + 60  # extra space for margins and the X button
        dlg.resize(ideal_width, 400)

        layout = QVBoxLayout(dlg)

        # Re-parent the scroll area (we'll return it when dialog closes)
        self.selected_reports_scroll_area.setParent(None)
        layout.addWidget(self.selected_reports_scroll_area)

        def _on_closed() -> None:
            # Return widget to splitter
            self.selected_reports_scroll_area.setParent(None)
            self.run_report_splitter.insertWidget(1, self.selected_reports_scroll_area)
            self._selected_reports_dialog = None

        dlg.finished.connect(lambda _status: _on_closed())
        self._selected_reports_dialog = dlg
        dlg.show()

    def _open_report_date_picker(self, report_name: str) -> None:
        """Open a date picker dialog for a specific report.

        Args:
            report_name (str): The name of the report to set the date for.
        """
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
        from PySide6.QtCore import QDate

        # Create a compact date picker dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Set Date for {report_name}")
        dialog.setModal(True)
        dialog.resize(380, 450)

        layout = QVBoxLayout(dialog)

        # Add a label
        label = QLabel(f"Select date for report: {report_name}")
        label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(label)

        # Create a smaller calendar widget
        calendar = MultiDateCalendar()
        calendar.setFixedSize(360, 380)  # Smaller than the main calendar

        # Set current date if one exists for this report
        current_date = self._report_dates.get(report_name)
        if current_date:
            qdate = QDate.fromString(current_date, "yyyy-MM-dd")
            if qdate.isValid():
                calendar.set_selected_dates([qdate])

        layout.addWidget(calendar)

        # Buttons
        button_layout = QHBoxLayout()

        clear_button = QPushButton("Clear")
        clear_button.clicked.connect(lambda: self._clear_report_date(report_name, dialog))

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)

        ok_button = QPushButton("OK")
        ok_button.setDefault(True)
        ok_button.clicked.connect(lambda: self._save_report_date(report_name, calendar, dialog))

        button_layout.addWidget(clear_button)
        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        dialog.exec()

    def _save_report_date(self, report_name: str, calendar: MultiDateCalendar, dialog: QDialog) -> None:
        """Save the selected date for a specific report.

        Args:
            report_name (str): The name of the report.
            calendar (MultiDateCalendar): The calendar widget.
            dialog (QDialog): The dialog to close.
        """
        selected_dates = calendar.get_selected_dates_as_strings()
        if selected_dates:
            # Use the first selected date
            date_str = selected_dates[0]
            self._report_dates[report_name] = date_str
            self._update_report_date_display(report_name, date_str)
            self._log_message(f"Set date for '{report_name}': {date_str}", "green")

        dialog.accept()

    def _clear_report_date(self, report_name: str, dialog: QDialog) -> None:
        """Clear the date for a specific report.

        Args:
            report_name (str): The name of the report.
            dialog (QDialog): The dialog to close.
        """
        self._report_dates.pop(report_name, None)
        self._update_report_date_display(report_name, "")
        self._log_message(f"Cleared date for '{report_name}'", "blue")
        dialog.accept()

    def _update_report_date_display(self, report_name: str, date_str: str) -> None:
        """Update the date display for a specific report.

        Args:
            report_name (str): The name of the report.
            date_str (str): The date string to display (empty to clear).
        """
        row_widget = self._selected_report_rows.get(report_name)
        if row_widget and hasattr(row_widget, '_date_label'):
            date_label = getattr(row_widget, '_date_label')
            if date_str:
                date_label.setText(date_str)
                date_label.setStyleSheet("font-size: 10px; color: #2c5aa0; font-weight: bold;")
            else:
                date_label.setText("")
                date_label.setStyleSheet("font-size: 10px; color: #666; font-style: italic;")

    def _update_calendar_visibility(self) -> None:
        """Update the visibility of the main calendar based on auto-find checkbox state."""
        auto_find_checked = self.auto_find_date_run_report_check.isChecked()

        # Hide main calendar when auto-find is checked, show when unchecked
        self.run_report_date_calendar.setVisible(not auto_find_checked)

        # Update tooltip and behavior
        if auto_find_checked:
            self._log_message("Main calendar hidden - using auto-found dates per report", "blue")
        else:
            self._log_message("Main calendar visible - manual date selection enabled", "blue")

    def _on_auto_find_date_monthly_toggled(self, checked: bool) -> None:
        """Handle the auto-find date checkbox toggle for monthly reports.

        Args:
            checked (bool): Whether the checkbox is checked.
        """
        if checked:
            # Get the SOID from the form
            soid = self.soid_edit.text().strip()
            if not soid:
                self.validator.show_error("Please enter a SOID before using auto-find date.")
                self.auto_find_date_monthly_check.setChecked(False)
                return

            # Import here to avoid circular imports
            from scripts.contrib.create_report_configs.utils.find_date_with_transactions import find_latest_transaction_date

            # Disable checkbox while querying
            self.auto_find_date_monthly_check.setEnabled(False)
            self._log_message(f"Querying Snowflake for latest transaction date for SOID: {soid}", "blue")

            try:
                # Find the latest transaction date
                latest_date = find_latest_transaction_date(soid)

                if latest_date:
                    self.date_with_transactions_edit.setText(latest_date)
                    self._log_message(f"Found latest transaction date: {latest_date}", "green")
                else:
                    self.validator.show_error(f"No transactions found for SOID: {soid}")
                    self.auto_find_date_monthly_check.setChecked(False)

            except Exception as e:
                self.validator.show_error(f"Error querying Snowflake: {str(e)}")
                self.auto_find_date_monthly_check.setChecked(False)
                self._log_message(f"Error querying Snowflake: {str(e)}", "red")
            finally:
                # Re-enable checkbox
                self.auto_find_date_monthly_check.setEnabled(True)

    def _on_auto_find_date_run_report_toggled(self, checked: bool) -> None:
        """Handle the auto-find date checkbox toggle for run report.

        Args:
            checked (bool): Whether the checkbox is checked.
        """
        if checked:
            # Get the reports to run
            reports_to_run = (
                list(self._selected_reports) if self._selected_reports else [self.run_report_name_edit.text().strip()]
            )

            if not reports_to_run or not reports_to_run[0]:
                self.validator.show_error("Please specify at least one report name before using auto-find date.")
                self.auto_find_date_run_report_check.setChecked(False)
                return

            # Import here to avoid circular imports
            from scripts.contrib.create_report_configs.utils.find_date_with_transactions import get_auto_find_dates_for_reports_batch
            from fine_reports.configuration.io import get_report_configuration_as_plain_dict

            # Disable checkbox while querying
            self.auto_find_date_run_report_check.setEnabled(False)
            self._log_message("Querying Snowflake for latest transaction dates for selected reports...", "blue")

            try:
                # Collect report-SOID pairs for batch processing
                report_soid_pairs = []
                environment = "dev" if self.dev_radio.isChecked() else "staging"

                for report_name in reports_to_run:
                    try:
                        # Get the report configuration to extract SOID
                        config = get_report_configuration_as_plain_dict(report_name, environment)

                        # Extract SOID from config
                        service_offering_ids = config.get("service_offering_ids", [])
                        if not service_offering_ids:
                            self._log_message(f"No SOID found in configuration for report: {report_name}", "orange")
                            continue

                        soid = service_offering_ids[0]  # Use first SOID
                        report_soid_pairs.append((report_name, soid))

                    except Exception as e:
                        self._log_message(f"Error processing report '{report_name}': {str(e)}", "red")
                        continue

                if not report_soid_pairs:
                    self.validator.show_error("No valid report configurations found")
                    self.auto_find_date_run_report_check.setChecked(False)
                    return

                # Use batch processing to get all dates at once
                report_dates = get_auto_find_dates_for_reports_batch(report_soid_pairs)

                # Process results
                successful_reports = []
                for report_name, soid in report_soid_pairs:
                    calculated_date = report_dates.get(report_name)

                    if calculated_date:
                        self._report_dates[report_name] = calculated_date
                        self._update_report_date_display(report_name, calculated_date)
                        successful_reports.append(report_name)
                        self._log_message(f"Report '{report_name}' (SOID: {soid}) -> {calculated_date}", "green")
                    else:
                        self._log_message(f"No transactions found for report '{report_name}' (SOID: {soid})", "orange")

                if successful_reports:
                    self._log_message(f"Set auto-found dates for {len(successful_reports)} reports", "green")
                else:
                    self.validator.show_error("No valid dates could be calculated for the selected reports")
                    self.auto_find_date_run_report_check.setChecked(False)

            except Exception as e:
                self.validator.show_error(f"Error calculating dates: {str(e)}")
                self.auto_find_date_run_report_check.setChecked(False)
                self._log_message(f"Error calculating dates: {str(e)}", "red")
            finally:
                # Re-enable checkbox
                self.auto_find_date_run_report_check.setEnabled(True)

    def _update_selected_reports_width(self) -> None:
        """Ensure the list area is wide enough for the longest report + X button."""
        if not self._selected_reports:
            return

        fm = self.fontMetrics()
        max_px = max(fm.horizontalAdvance(name) for name in self._selected_reports)
        ideal_w = max_px + 60  # margin + remove button

        self.selected_reports_scroll_area.setMinimumWidth(ideal_w)
        self.selected_reports_container.setMinimumWidth(ideal_w)
        # Header widgets match
        self.run_report_splitter.widget(0).setMinimumWidth(ideal_w)
        self.run_report_splitter.setMinimumWidth(ideal_w)

    def _on_clear_all_clicked(self) -> None:
        """Remove all selected reports instantly."""
        for name in list(self._selected_reports):
            self._remove_selected_report(name)

    # ---------------------------------------------------------------------
    # Log area utilities
    # ---------------------------------------------------------------------

    def _on_log_area_context_menu(self, point):
        """Show a context menu with 'Clear Logs' when right-clicking the log area."""
        menu = QMenu(self)
        clear_action = menu.addAction("Clear Logs")

        # Map the global position so the menu shows at the correct cursor location
        action = menu.exec(self.log_area.mapToGlobal(point))
        if action == clear_action:
            self._clear_log_area()

    def _clear_log_area(self) -> None:
        """Clear all content from the logger window to improve performance."""
        self.log_area.clear()
