"""Custom integration module generation and management."""

from pathlib import Path
import re
import subprocess
import sys
from typing import Any

from scripts.contrib.create_report_configs.models.constants import CUSTOM_INTEGRATION_DIR
from scripts.contrib.create_report_configs.utils.helpers import generate_output_filename


def generate_custom_integration_file_monthly_report(
    class_name: str,
    report_name: str,
    soid: str,
    daily_report_name: str | None = None,
    date_with_transactions: str | None = None,
    overwrite_existing: bool = False,
    advanced_selections: dict[str, str] | None = None,
    remittance_type: str | None = None,
) -> dict[str, Any] | None:
    """Prepare parameters for creating the custom integration Python file for a new monthly report.

    Args:
        class_name (str): The class name to be used in the file.
        report_name (str): The report name, used to derive the filename.
        soid (str): The Service Offering ID, used for other purposes.
        daily_report_name (str | None, optional): The name of the corresponding daily report for column structure.
        date_with_transactions (str | None, optional): A date (YYYY-MM-DD) that has transactions for this report.
        overwrite_existing (bool): Whether to overwrite existing files.
        advanced_selections (dict[str, str] | None, optional): Advanced remittance selections from GUI.
        remittance_type (str | None, optional): The remittance type ("Combined" or "Return").

    Returns:
        dict[str, Any] | None: A dictionary with edit_file parameters or None if skipping.
    """
    filename = f"{report_name}.py"
    target_path = CUSTOM_INTEGRATION_DIR / filename

    # Only skip if file exists AND overwrite is disabled
    if target_path.exists() and not overwrite_existing:
        print(f"Info: Custom integration file {target_path} already exists. Skipping file creation.")
        return None

    # Ensure daily_report_name is the config name (with underscores), not output_file_name (with hyphens)
    # If not provided, infer from monthly report name
    if not daily_report_name and report_name.endswith("_monthly_recap"):
        daily_report_name = report_name.replace("_monthly_recap", "_financial_remittance")

    report_name_base = daily_report_name.replace("_financial_remittance", "") if daily_report_name else soid
    output_name = generate_output_filename(f"{report_name_base}_monthly_recap", "")
    if output_name.endswith("-financial-remittance"):
        output_name = output_name[: -len("-financial-remittance")]

    # ------------------------------------------------------------------
    # Discover column definitions
    # ------------------------------------------------------------------

    report_attribs_content = """REPORT_ATTRIBS = {\n    \"report\": {},\n}\n\n\n"""

    helper_success = False

    # Attempt dynamic discovery via pull_report_column_names.py script if required inputs provided
    if daily_report_name and date_with_transactions:
        helper_script = Path(__file__).parent.parent / "utils" / "pull_report_column_names.py"
        if helper_script.exists():
            cmd = [
                sys.executable,
                str(helper_script),
                "--report_name",
                daily_report_name,  # Always pass the config name (underscored)
                "--report_date",
                date_with_transactions,
            ]

            # Add advanced selections and remittance type if provided
            if advanced_selections and remittance_type:
                import json

                cmd.extend(
                    [
                        "--advanced_selections",
                        json.dumps(advanced_selections),
                        "--remittance_type",
                        remittance_type,
                    ]
                )

            try:
                proc = subprocess.run(cmd, capture_output=True, text=True, check=True)
                match = re.search(r"REPORT_ATTRIBS\s*=\s*{[\s\S]+?}\s*$", proc.stdout, re.MULTILINE)
                if match:
                    report_attribs_content = match.group(0) + "\n\n\n"
                    helper_success = True
                    print("REPORT_ATTRIBS generated via helper script.")
                else:
                    print("Helper script did not output REPORT_ATTRIBS block..")
            except subprocess.CalledProcessError as err:
                print(f"Helper script failed with exit code {err.returncode}: {err.stderr}..")
            except Exception as exc:
                print(f"Unexpected error running helper script: {exc}..")

    # Fallback: Empty columns list if helper failed or not executed
    if not helper_success:
        # Check if we need both combined and return columns
        needs_return_columns = False
        if remittance_type == "Combined" and advanced_selections:
            # Check if any advanced selection contains "RETURN"
            for selection_value in advanced_selections.values():
                if selection_value and "RETURN" in str(selection_value):
                    needs_return_columns = True
                    break

        if needs_return_columns:
            # Create both combined and return columns (identical content)
            report_attribs_content = (
                "REPORT_ATTRIBS = {\n"
                '    "report": {\n'
                '        "types": ["combined", "return"],\n'
                f'        "filter_mask": "payit-{report_name_base}-TYPE",\n'
                f'        "out_filename": "payit-{report_name_base}-TYPE-monthly_recap-DATE.csv",\n'
                '        "sort_columns": ["Transaction Date", "Transaction ID"],\n'
                '        "transaction_id_column": "Transaction ID",\n'
                '        "groupby_column": "Payment Type",\n'
                '        "amount_column": "Remittance Amount",\n'
                '        "summary_stock_titles": ["Summary", "Total"],\n'
                '        "report_columns_combined": [],\n'
                '        "report_columns_return": [],\n'
                "    },\n"
                "}\n\n\n"
            )
        else:
            # Only combined columns
            report_attribs_content = (
                "REPORT_ATTRIBS = {\n"
                '    "report": {\n'
                '        "types": ["combined"],\n'
                f'        "filter_mask": "payit-{report_name_base}-TYPE",\n'
                f'        "out_filename": "payit-{report_name_base}-TYPE-monthly_recap-DATE.csv",\n'
                '        "sort_columns": ["Transaction Date", "Transaction ID"],\n'
                '        "transaction_id_column": "Transaction ID",\n'
                '        "groupby_column": "Payment Type",\n'
                '        "amount_column": "Remittance Amount",\n'
                '        "summary_stock_titles": ["Summary", "Total"],\n'
                '        "report_columns_combined": [],\n'
                "    },\n"
                "}\n\n\n"
            )

    # Construct the file content
    file_content = """\"""Reads reports for given month, cats and sorts them and handles email delivery.\"""\n\n"""
    file_content += """from typing import override\n\n"""
    file_content += """from opentelemetry import trace\n"""
    file_content += """from opentelemetry.trace import Tracer\n\n"""
    file_content += """from payit_remittance_report.custom_integration.base_monthly_recap import BaseMonthlyRecap\n"""
    file_content += """from payit_remittance_report.tracing import instrument\n\n"""
    file_content += """tracer: Tracer = trace.get_tracer(__name__)\n\n"""
    file_content += report_attribs_content
    file_content += """@instrument\n"""
    file_content += f"""class {class_name}(BaseMonthlyRecap):\n"""
    file_content += """    def __init__(self):\n"""
    file_content += """        super().__init__(REPORT_ATTRIBS["report"])\n\n"""
    file_content += """    @override\n"""
    file_content += """    def run_report(self) -> None:\n"""
    file_content += """        self._process_reports()\n"""

    return {
        "target_file": str(target_path),
        "code_edit": file_content,
        "instructions": f"Create new custom integration file for {class_name}.",
    }


def generate_custom_integration_module(
    report_name: str,
    soid: str,
    overwrite_existing: bool,
    daily_report_name: str | None = None,
    date_with_transactions: str | None = None,
    advanced_selections: dict[str, str] | None = None,
    remittance_type: str | None = None,
) -> tuple[dict[str, str], dict[str, Any] | None, str | None, str | None]:
    """Generate module dict, prepare edit params for .py file, and return class/module for wrapper.

    Args:
        report_name (str): The report name.
        soid (str): The service offering ID.
        overwrite_existing (bool): Whether to overwrite existing files (passed to .py file creation logic).
        daily_report_name (str | None, optional): The name of the corresponding daily report for column structure.
        date_with_transactions (str | None, optional): A date (YYYY-MM-DD) that has transactions.
        advanced_selections (dict[str, str] | None, optional): Advanced remittance selections from GUI.
        remittance_type (str | None, optional): The remittance type ("Combined" or "Return").

    Returns:
        tuple containing:
            - dict[str, str]: The custom_integration_module dictionary for JSON config.
            - dict[str, Any] | None: Edit parameters for the custom integration .py file.
            - str | None: Class name for the wrapper map.
            - str | None: Module path for the wrapper map.
    """
    class_name_base = "".join(word.capitalize() for word in soid.split("_"))
    class_name = f"{class_name_base}MonthlyRecap"
    module_path = f"payit_remittance_report.custom_integration.{report_name}"

    custom_module_dict = {
        "class_name": class_name,
        "module_path": module_path,
    }

    # Generate params for the .py file, passing the overwrite parameter
    py_file_edit_params = generate_custom_integration_file_monthly_report(
        class_name,
        report_name,
        soid,
        daily_report_name,
        date_with_transactions,
        overwrite_existing,
        advanced_selections,
        remittance_type,
    )

    return custom_module_dict, py_file_edit_params, class_name, module_path
