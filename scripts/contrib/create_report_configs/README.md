# Remittance Report Configuration Generator

A Python application for generating remittance report configuration files (JSON and YAML) with a user-friendly GUI interface.

---

## Overview

The `create_report_configs` application is a tool for users to easily generate, validate, and manage remittance report configuration files. It provides a graphical interface (GUI) for entering report parameters and generates the necessary JSON and YAML files for the remittance reporting configuration. The tool is designed to be modular, maintainable, and extensible, supporting a variety of service types and delivery methods.

**Key Goals:**
- Simplify the creation of new remittance report configurations
- Enforce validation and scheduling rules to prevent conflicts
- Support both standard daily and monthly reports
- Enable easy updates as requirements evolve

---

## Quick Start

Run the application from the project root:
`poetry run ./scripts/contrib/create_report_configs/main.py`

## Features

### 1. User-Friendly GUI
- PySide6-based interface for entering all report parameters
- Tooltips and validation feedback for every field

### 2. Comprehensive Validation & Checks
- **Field Validation:** Ensures all required fields are filled and formatted correctly
- **Report Name Rules:** Enforces naming conventions for daily/monthly reports
- **Delivery Config Checks:** Validates email/FTP settings and required fields
- **Schedule Conflict Detection:**
  - Prevents scheduling more than 5 reports at the same time slot
  - Ensures monthly reports are scheduled at least 10 minutes after daily reports (if both exist)
  - Warns about conflicts with existing schedules for the same SOID
- **Add-On Fields:** Dynamically includes fields for IVR, Integrated POS, etc., as needed

### 3. View Available Time Slots
- Use the "Show Available Times" button in the GUI to see all time slots (between 3:00–8:00 AM) with 4 or fewer reports scheduled
- Helps you pick a time that avoids conflicts and stays within system limits

### 4. File Generation
- **JSON Configs:** Written to `payit_remittance_report/configs/`
- **YAML Schedules:** Written to `fine_reports/configuration/resources/scheduled_job/{staging,prod}/`
- **Custom Integration Modules:** For monthly reports, Python modules are generated in `payit_remittance_report/custom_integration/`
- **Wrapper Updates:** Automatically updates the integration map in `remittance_report_wrapper.py` as needed

### 5. Run Report Testing
- **Test Parameters:** Enter a date with transactions and easilyrun any report to validate its configuration
- **Log Output:** View real-time log output from the report execution in the GUI

### 6. Extensible & Maintainable
- Modular codebase with clear separation of GUI, business logic, models, and utilities
- Easy to add new service types (or modify them), validators, etc.
- Full type hints and Google-style docstrings throughout

---

## Code Structure & How to Update Fields

The application is organized for clarity and extensibility:

```
create_report_configs/
├── core/         # Business logic, config generators, validators
├── gui/          # GUI components (main window, widgets, validators)
├── models/       # Data models, constants, service configs
├── utils/        # File operations, scheduling, helpers
├── integrations/ # Custom integration and wrapper management
```

### How to Update Service Fields

- **Service-Specific Fields:**
  - All service types and their required fields are defined in `models/service_configs.py` in the `SERVICE_MAPPING` dictionary.
  - To add or remove fields for a service, update the `ServiceConfig` for that service type.
  - Example:
    ```python
    SERVICE_MAPPING["New Service"] = ServiceConfig(
        model=[...],
        sql="sql_file_name",
        service_names=[...],
        use_all_sdk_fields=True,
        use_base_remittance_fields=True,
        use_forms_data=False,
        additional_fields=[{"name": "Custom Field", "source": "custom_source"}]
    )
    ```
- **Add-Ons:**
  - Add-on fields (e.g., IVR, Integrated POS) are managed in `get_addon_additional_fields()` in `models/service_configs.py`.
- **Validation Rules:**
  - All business logic and field validation is in `core/validators.py`.
  - To add new validation rules, add a new function and include it in the `validate_all_inputs()` sequence.
- **GUI Fields:**
  - To add new fields to the GUI, update `gui/main_window.py`.
  - For new service types, add them to the dropdown in `SERVICE_OFFERING_OPTIONS` in `models/constants.py`.

- **Monthly Report Generation:**
  - For monthly reports, the tool can generate a Python module for custom processing. This is placed in `payit_remittance_report/custom_integration/` and automatically registered in the wrapper.
- **Logging:**
  - All actions and errors are logged in the GUI log area for easy troubleshooting.

---

## Post-Generation Steps

1. **Review Generated Files:** Always review the generated JSON and YAML files for accuracy.
2. **Update YAML Configs:** Run `./regenerate-yaml-configs.sh` to generate all YAML configs.
3. **Create Google Groups:** Set up email groups for staging and production as needed.
4. **Test in Staging:** Run and verify the configuration in the staging environment before production.

---

## Troubleshooting

- **Import Errors:** Make sure you are running from the project root directory.
- **Schedule Conflicts:** The tool will detect and warn about timing conflicts. Use the "Show Available Times" feature to resolve.
- **File Exists:** Use the "Overwrite Existing Files" option if you need to replace existing configs.

---

## Support

For questions or issues, contact Caleb Simmons (<EMAIL>).
