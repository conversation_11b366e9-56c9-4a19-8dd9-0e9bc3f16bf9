"""Data model representing a subset of values pulled from an existing daily report configuration.

This snapshot is used by the GUI to pre-fill the Monthly-only workflow.  It purposefully contains *only* the
fields the GUI needs so that the GUI layer stays decoupled from the raw JSON structure.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Literal


@dataclass
class DailyReportSnapshot:
    """Values extracted from a daily report config that are relevant to monthly creation."""

    # Basic information
    app_name: str
    soid: str
    service_offering_type: str
    client_timezone: str

    # Delivery configuration (mandatory flags first)
    email_selected: bool
    ftp_selected: bool

    # Remittance handling (non-default must come before any defaulted fields)
    remittance_type: Literal["Combined", "Return"]

    # Optional / defaulted values
    advanced_selections: dict[str, str] | None = None
    email_subject: str | None = None
    ftp_filepath: str | None = None
    email_base: str | None = None  # ``None`` means *auto* should remain selected in the GUI
    output_file_name: str | None = None  # The output_file_name from the JSON config, used for S3 file lookup
