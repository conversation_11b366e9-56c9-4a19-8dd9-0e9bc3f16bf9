"""Constants and configuration values for the report config generator."""

from pathlib import Path

# Determine project root assuming this script is always in project_root/scripts/contrib/
PROJECT_ROOT = Path(__file__).resolve().parents[4]
JSON_CONFIG_DIR = PROJECT_ROOT / "payit_remittance_report" / "configs"
STAGING_SCHEDULED_JOB_DIR = PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "staging"
PROD_SCHEDULED_JOB_DIR = PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "prod"
CA_PROD_SCHEDULED_JOB_DIR = PROJECT_ROOT / "fine_reports" / "configuration" / "resources" / "scheduled_job" / "ca-prod"
CUSTOM_INTEGRATION_DIR = PROJECT_ROOT / "payit_remittance_report" / "custom_integration"
WRAPPER_FILE_PATH = PROJECT_ROOT / "payit_remittance_report" / "remittance_report_wrapper.py"
MAP_VAR_NAME = "CUSTOM_INTEGRATION_MAP"

DAILY_REPORT_SUFFIX = "_financial_remittance"
MONTHLY_REPORT_SUFFIX = "_monthly_recap"

# Environment options
ENVIRONMENT_OPTIONS = ["dev", "staging"]

# Tooltips Dictionary
TOOLTIPS: dict[str, str] = {
    "App Name:": '<p style="width: 250px;">'
    "Enter the EXACT name of the client application found in OURO."
    "<br><br>This is used in filenames and internal identifiers."
    "</p>",
    "Report Name:": '<p style="width: 250px;">'
    f"Enter the report name. <br><br>If Daily frequency is selected (alone or with Monthly), "
    f"it must end with '{DAILY_REPORT_SUFFIX}'.<br>"
    f"If only Monthly frequency is selected, it must end with '{MONTHLY_REPORT_SUFFIX}'.<br>"
    f"When both frequencies are selected, this becomes the Daily report name and a separate "
    f"Monthly Report Name field will appear.<br>"
    "Ex: client_state_service_financial_remittance or client_state_service_monthly_recap."
    "<br><br>"
    "</p>",
    "Client Time Zone:": '<p style="width: 250px;">Select the time zone that the client is in.</p>',
    "Remittance Type:": '<p style="width: 250px;">'
    'Choose "Return" (generates success/return files) or "Combined" (generates one combined file).'
    '<br><br>Affects "report_type" in JSON.'
    "</p>",
    "Service Offering Type:": '<p style="width: 250px;">'
    'Select the Service Offering Type (e.g., "Property Tax", "Non-Integrated POS").'
    '<br><br>Affects "report_type" in JSON.'
    "</p>",
    "SOID:": '<p style="width: 250px;">'
    "Enter the specific Service Offering ID associated with this report configuration."
    "</p>",
    "Delivery Config:": '<p style="width: 250px;">'
    "Select the delivery method(s) for the report: Email, FTP, or Both."
    "</p>",
    "Delivery Frequency:": '<p style="width: 250px;">'
    "Choose how often the report should be generated and delivered. Both can be selected."
    "</p>",
    "Daily Schedule Time (HH:mm):": '<p style="width: 250px;">'
    "Set the time (24-hour format) for the daily report job to run."
    "</p>",
    "Monthly Schedule Time (HH:mm):": '<p style="width: 250px;">'
    "Set the time (24-hour format) for the monthly report job to run."
    "</p>",
    "Monthly Day of Month:": '<p style="width: 250px;">'
    "Select the day of the month (1-28) on which the monthly report should run."
    "</p>",
    "FTP Filepath:": '<p style="width: 250px;">Enter the specific directory path on the FTP server.</p>',
    "FTP/SFTP Credentials:": '<p style="width: 250px;">'
    "Select the Kubernetes Secret containing the FTP/SFTP credentials for this report delivery."
    "</p>",
    "Email Subject:": (
        '<p style="width: 250px;">Enter the desired subject line for the email. '
        "Use the exact word 'DATE' at the end as a placeholder for the report date.</p>"
    ),
    "Add Ons:": (
        '<p style="width: 250px;">Select additional integrations to include. '
        "IVR adds IVR confirmation number field, Integrated POS adds POS confirmation number field.</p>"
    ),
    "Add Additional Fields:": (
        '<p style="width: 250px;">Check this to add custom additional fields to the report. '
        'Enter field names and their corresponding data sources. Use the "+" button to add more fields. '
        "Each field requires both a name (display name in report) and source (database column or field name).</p>"
    ),
    "Email Base (Google Group):": (
        '<p style="width: 250px;">Choose how the email base (used for Google Group name) is generated."'
        '<br><br>If "Auto Generate" is checked, the email base will be generated from the report name.'
        '<br><br>If "Manual Input" is checked, the email base will be manually entered.'
        "<br><br><EMAIL> = prod reports."
        "<br><br><EMAIL> = staging reports."
        "<br><br>Only enter the email base, not the @payitgov.com. Create prod and staging Google Groups."
        "</p>"
    ),
    "Cancellation:": (
        '<p style="width: 250px;">Select handling for cancellation transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Chargeback:": (
        '<p style="width: 250px;">Select handling for chargeback transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Failure:": (
        '<p style="width: 250px;">Select handling for failure transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Refund:": (
        '<p style="width: 250px;">Select handling for refund transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Same Day Cancellation:": (
        '<p style="width: 250px;">Select handling for same day cancellation transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Same Day Failure:": (
        '<p style="width: 250px;">Select handling for same day failure transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Same Day Refund:": (
        '<p style="width: 250px;">Select handling for same day refund transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Success:": (
        '<p style="width: 250px;">Select handling for success transactions. '
        "'default' uses standard logic based on Remittance Type.</p>"
    ),
    "Advanced Remittance Selections:": (
        '<p style="width: 250px;">Check this to manually override status handling for specific transaction types.</p>'
    ),
    "Email Base Auto Generate:": (
        '<p style="width: 250px;">Check to automatically generate the email base from the report name.</p>'
    ),
    "Show Available Times:": (
        '<p style="width: 250px;">Analyze schedule and show available times with 4 or fewer reports scheduled.</p>'
    ),
    "Create JSON Only:": (
        '<p style="width: 250px;">Check this if you only want to create only the JSON config file, '
        "and not the YAML schedule files.</p>"
    ),
    "Overwrite Existing Files:": (
        '<p style="width: 250px;">Check this to allow the script to overwrite existing configuration files '
        "with the same name.</p>"
    ),
    "Enable Run Report:": (
        '<p style="width: 250px;">Check this to enable running the report with test parameters.</p>'
    ),
    "Environment Selection:": (
        '<p style="width: 250px;">Select the environment to run the report against:<br><br>'
        "• <b>Dev:</b> Development environment for testing<br>"
        "• <b>Staging:</b> Staging environment for validation</p>"
    ),
    "Run Report Date:": (
        '<p style="width: 350px;">'
        "<b>Multi-Date Selection:</b><br>"
        "• <b>Single click:</b> Select one date<br>"
        "• <b>Ctrl+click:</b> Add/remove individual dates<br>"
        "• <b>Shift+click:</b> Select date range<br>"
        "• <b>Clear Selections:</b> Reset all dates<br><br>"
        "Select dates that have transactions for testing."
        "</p>"
    ),
    "Auto-populate Report Name:": (
        '<p style="width: 250px;">Automatically use the report name from the main form.</p>'
    ),
    "Run Report Button:": (
        '<p style="width: 250px;">Run the report with specified parameters to test the configuration.</p>'
    ),
    "Date With Transactions:": (
        '<p style="width: 250px;">Enter a date (YYYY-MM-DD) that has transactions for this report. '
        "This is used to generate proper column definitions for monthly reports.</p>"
    ),
    "Daily Report Name:": (
        '<p style="width: 250px;">Enter the name of the corresponding daily report for this monthly report. '
        "This is used to determine the column structure.</p>"
    ),
    "Monthly Report Name:": (
        '<p style="width: 250px;">The name for the monthly report when both Daily and Monthly '
        "frequencies are selected. Auto-generated by replacing 'financial_remittance' with "
        "'monthly_recap' in the main Report Name.</p>"
    ),
    "Auto-find date (Monthly):": (
        '<p style="width: 250px;">Automatically query Snowflake to find the most recent transaction date for the specified SOID '
        "and populate the Date With Transactions field. This helps ensure you have the correct date "
        "for monthly report generation.</p>"
    ),
    "Auto-find date (Run Report):": (
        '<p style="width: 250px;">Automatically query Snowflake to find the most recent transaction date for each selected report\'s SOID. '
        "For daily reports, uses the exact transaction date. For monthly reports, uses the end of the month "
        "containing the transaction date. This ensures each report runs with the appropriate date.</p>"
    ),
}

# Timezone options
TIMEZONE_OPTIONS = ["US/Eastern", "US/Central", "US/Mountain", "US/Pacific", "US/Alaska"]

# Service offering options
SERVICE_OFFERING_OPTIONS = [
    "Property Tax",
    "Citations",
    "Non-Integrated Form",
    "Non-Integrated POS",
    "SDK",
    "Water",
    "Verticalless",
]
