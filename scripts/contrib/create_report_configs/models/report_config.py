"""Data models for report configurations."""

from dataclasses import dataclass

from PySide6.QtCore import QTime


@dataclass
class FormData:
    """Data collected from the GUI form."""

    # Basic information
    app_name: str
    report_name_input: str
    client_timezone: str
    remittance_type: str
    service_offering_type: str
    soid: str

    # Delivery configuration
    email_selected: bool
    ftp_selected: bool
    delivery_method_str: str
    ftp_path_val: str | None
    email_subject_val: str | None
    daily_email_subject: str | None
    monthly_email_subject: str | None

    # Add-ons
    addons: list[str]

    # Manual additional fields
    manual_additional_fields: list[dict[str, str]]

    # Other options
    ftp_credential_val: str
    overwrite_existing_val: bool

    # Email base
    email_base_val: str | None
    is_auto_email_base: bool

    # Advanced remittance selections
    advanced_selections_val: dict[str, str] | None

    # Frequency
    daily_frequency_selected: bool
    monthly_frequency_selected: bool

    # Schedule times
    daily_schedule_time: QTime
    monthly_schedule_time: QTime
    monthly_day_of_month_val: int

    # For monthly reports
    date_with_transactions: str
    daily_report_name: str
    monthly_report_name: str

    # Processing options
    create_json_only: bool
    report_name: str = ""  # Validated report name


@dataclass
class RunReportData:
    """Data for running a report."""

    report_name: str
    report_date: str  # Kept for backward compatibility
    report_dates: list[str]  # Multiple dates support
    auto_populate_name: bool
    environment: str = "staging"  # Default to staging


@dataclass
class ReportConfig:
    """Configuration for generating a report."""

    app_name: str
    report_name: str
    client_timezone: str
    remittance_type: str
    service_offering_type: str
    soid: str
    delivery_method: str
    frequency: str
    cron_schedule: str
    ftp_filepath: str | None = None
    email_subject: str | None = None
    addons: list[str] | None = None
    advanced_selections: dict[str, str] | None = None
    manual_email_base: str | None = None
    manual_additional_fields: list[dict[str, str]] | None = None
    ftp_credential: str | None = None
    daily_report_name: str | None = None
    date_with_transactions: str | None = None


@dataclass
class ValidationResult:
    """Result of a validation operation."""

    is_valid: bool
    error_message: str
    suggested_alternatives: list[str] | None = None
